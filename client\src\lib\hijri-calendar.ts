// Simplified Hijri date conversion
// In production, use a proper Hijri calendar library like moment-hijri

interface IslamicEvent {
  name: string;
  hijriDate: string;
  gregorianDate: string;
}

export const islamicEvents: IslamicEvent[] = [
  {
    name: "<PERSON><PERSON> and <PERSON>r<PERSON>",
    hijriDate: "27 Rajab",
    gregorianDate: "2024-03-15",
  },
  {
    name: "First of Sha'ban",
    hijriDate: "1 Sha'ban",
    gregorianDate: "2024-03-20",
  },
  {
    name: "<PERSON><PERSON> al-Bara'at",
    hijriDate: "15 Sha'ban",
    gregorianDate: "2024-04-03",
  },
  {
    name: "First of Ramadan",
    hijriDate: "1 Ramadan",
    gregorianDate: "2024-04-18",
  },
  {
    name: "<PERSON><PERSON> al-Qadr",
    hijriDate: "27 Ramadan",
    gregorianDate: "2024-05-14",
  },
];

const hijriMonths = [
  "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON>",
  "<PERSON><PERSON>w<PERSON>", "<PERSON><PERSON> al-than<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>",
  "Ramadan", "<PERSON><PERSON>", "<PERSON><PERSON> <PERSON><PERSON>h", "Dhu al-Hijjah"
];

const hijriMonthsArabic = [
  "محرم", "صفر", "ربيع الأول", "ربيع الثاني",
  "جمادى الأولى", "جمادى الثانية", "رجب", "شعبان",
  "رمضان", "شوال", "ذو القعدة", "ذو الحجة"
];

// Simplified conversion - in production use a proper library
export function getHijriDate(gregorianDate: Date): string {
  // This is a very simplified approximation
  // Use moment-hijri or similar library for accurate conversion
  const hijriYear = 1445; // Approximate current Hijri year
  const monthIndex = Math.floor(Math.random() * 12); // Simplified
  const day = Math.floor(Math.random() * 29) + 1; // Simplified
  
  return `${day} ${hijriMonthsArabic[monthIndex]} ${hijriYear}`;
}

export function getHijriMonth(gregorianDate: Date): string {
  // Simplified - use proper library in production
  const monthIndex = Math.floor(Math.random() * 12);
  return `${hijriMonthsArabic[monthIndex]} ١٤٤٥`;
}

export function getHijriYear(gregorianDate: Date): string {
  return "١٤٤٥"; // Simplified
}

export function convertGregorianToHijri(gregorianDate: Date): {
  year: number;
  month: number;
  day: number;
  monthName: string;
} {
  // Simplified conversion - use proper library
  return {
    year: 1445,
    month: 7,
    day: 15,
    monthName: hijriMonths[6], // Rajab
  };
}
