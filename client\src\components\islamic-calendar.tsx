import { useState } from "react";
import { Card } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { ChevronLeft, ChevronRight } from "lucide-react";
import { getHijriDate, getHijriMonth, getHijriYear, islamicEvents } from "@/lib/hijri-calendar";

export function IslamicCalendar() {
  const [currentDate, setCurrentDate] = useState(new Date());
  
  const hijriDate = getHijriDate(currentDate);
  const gregorianDate = currentDate.toLocaleDateString('en-US', { 
    year: 'numeric', 
    month: 'long', 
    day: 'numeric' 
  });

  const currentHijriMonth = getHijriMonth(currentDate);
  const currentHijriYear = getHijriYear(currentDate);

  // Generate calendar days for the current month
  const generateCalendarDays = () => {
    const year = currentDate.getFullYear();
    const month = currentDate.getMonth();
    
    const firstDay = new Date(year, month, 1);
    const lastDay = new Date(year, month + 1, 0);
    const daysInMonth = lastDay.getDate();
    const startingDayOfWeek = firstDay.getDay();
    
    const days = [];
    
    // Previous month's trailing days
    const prevMonth = new Date(year, month - 1, 0);
    for (let i = startingDayOfWeek - 1; i >= 0; i--) {
      days.push({
        day: prevMonth.getDate() - i,
        isCurrentMonth: false,
        date: new Date(year, month - 1, prevMonth.getDate() - i),
      });
    }
    
    // Current month's days
    for (let day = 1; day <= daysInMonth; day++) {
      days.push({
        day,
        isCurrentMonth: true,
        date: new Date(year, month, day),
        isToday: day === currentDate.getDate() && month === currentDate.getMonth() && year === currentDate.getFullYear(),
      });
    }
    
    // Next month's leading days
    const totalCells = 42; // 6 rows × 7 days
    const remainingCells = totalCells - days.length;
    for (let day = 1; day <= remainingCells; day++) {
      days.push({
        day,
        isCurrentMonth: false,
        date: new Date(year, month + 1, day),
      });
    }
    
    return days;
  };

  const calendarDays = generateCalendarDays();

  const navigateMonth = (direction: 'prev' | 'next') => {
    const newDate = new Date(currentDate);
    if (direction === 'prev') {
      newDate.setMonth(newDate.getMonth() - 1);
    } else {
      newDate.setMonth(newDate.getMonth() + 1);
    }
    setCurrentDate(newDate);
  };

  const upcomingEvents = islamicEvents.filter(event => {
    const eventDate = new Date(event.gregorianDate);
    return eventDate >= new Date();
  }).slice(0, 3);

  return (
    <section className="p-3 sm:p-4 w-full">
      <Card className="p-4 sm:p-6 mx-auto max-w-lg">
        <div className="flex justify-between items-center mb-6">
          <h2 className="text-xl font-semibold text-gray-800 dark:text-white">
            Islamic Calendar
          </h2>
          <div className="text-right">
            <p className="text-sm text-gray-600 dark:text-gray-400">
              {gregorianDate}
            </p>
            <p className="font-amiri text-lg text-emerald-600 dark:text-emerald-400">
              {hijriDate}
            </p>
          </div>
        </div>

        {/* Calendar Header */}
        <div className="mb-4">
          <div className="flex justify-between items-center mb-4">
            <Button
              variant="ghost"
              size="sm"
              onClick={() => navigateMonth('prev')}
            >
              <ChevronLeft className="h-4 w-4" />
            </Button>
            <h3 className="font-amiri text-lg font-semibold text-gray-800 dark:text-white">
              {currentHijriMonth} {currentHijriYear}
            </h3>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => navigateMonth('next')}
            >
              <ChevronRight className="h-4 w-4" />
            </Button>
          </div>
        </div>

        {/* Calendar Grid */}
        <div className="mb-6">
          {/* Days Header */}
          <div className="grid grid-cols-7 gap-1 mb-2">
            {['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'].map((day) => (
              <div key={day} className="text-center text-xs font-medium text-gray-500 dark:text-gray-400 p-2">
                {day}
              </div>
            ))}
          </div>
          
          {/* Calendar Days */}
          <div className="grid grid-cols-7 gap-1">
            {calendarDays.map((day, index) => (
              <div
                key={index}
                className={`text-center p-2 text-sm ${
                  day.isCurrentMonth
                    ? day.isToday
                      ? "bg-emerald-600 text-white rounded-lg font-semibold"
                      : "text-gray-800 dark:text-white hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg cursor-pointer"
                    : "text-gray-400 dark:text-gray-600"
                }`}
              >
                {day.day}
              </div>
            ))}
          </div>
        </div>

        {/* Islamic Events */}
        <div className="bg-gray-50 dark:bg-gray-800 rounded-xl p-4">
          <h4 className="font-semibold text-gray-800 dark:text-white mb-3">
            Upcoming Events
          </h4>
          <div className="space-y-3">
            {upcomingEvents.length > 0 ? (
              upcomingEvents.map((event, index) => (
                <div key={index} className="flex items-center space-x-3">
                  <div className="w-2 h-2 bg-emerald-500 rounded-full"></div>
                  <div>
                    <p className="text-sm font-medium text-gray-800 dark:text-white">
                      {event.name}
                    </p>
                    <p className="text-xs text-gray-600 dark:text-gray-400">
                      {event.hijriDate}
                    </p>
                  </div>
                </div>
              ))
            ) : (
              <p className="text-sm text-gray-600 dark:text-gray-400">
                No upcoming events this month
              </p>
            )}
          </div>
          
          {/* Current Month Info */}
          <div className="mt-4 pt-3 border-t border-gray-200 dark:border-gray-700">
            <Badge variant="outline" className="text-xs">
              Current Hijri Month: {currentHijriMonth}
            </Badge>
          </div>
        </div>
      </Card>
    </section>
  );
}
