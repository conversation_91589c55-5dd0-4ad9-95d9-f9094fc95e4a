import { Card } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { FileImage, Download, ExternalLink } from "lucide-react";

interface QuranPageFallbackProps {
  pageNumber: number;
  language: "en" | "ar";
  onRetry: () => void;
}

export function QuranPageFallback({ pageNumber, language, onRetry }: QuranPageFallbackProps) {
  const downloadLinks = [
    {
      name: language === "ar" ? "جامعة الملك سعود" : "King Saud University",
      url: `https://quran.ksu.edu.sa/png_big/${pageNumber}.png`,
    },
    {
      name: language === "ar" ? "موقع القرآن الكريم" : "Quran.com",
      url: `https://cdn.qurancdn.com/images/pages/page${pageNumber.toString().padStart(3, '0')}.png`,
    },
    {
      name: language === "ar" ? "تنزيل" : "Tanzil",
      url: `https://tanzil.net/images/pages/page${pageNumber.toString().padStart(3, '0')}.png`,
    }
  ];

  return (
    <Card className="p-6 text-center">
      <FileImage className="h-16 w-16 text-gray-400 mx-auto mb-4" />
      
      <h3 className="text-lg font-semibold mb-2">
        {language === "ar" ? "لا يمكن تحميل الصفحة" : "Unable to Load Page"}
      </h3>
      
      <p className="text-gray-600 dark:text-gray-400 mb-4">
        {language === "ar" 
          ? `صفحة ${pageNumber} من القرآن الكريم غير متاحة حالياً`
          : `Page ${pageNumber} of the Quran is currently unavailable`
        }
      </p>

      <div className="space-y-3">
        <Button onClick={onRetry} className="w-full">
          {language === "ar" ? "إعادة المحاولة" : "Retry"}
        </Button>

        <div className="border-t pt-3">
          <p className="text-sm text-gray-500 mb-2">
            {language === "ar" ? "أو جرب هذه المصادر:" : "Or try these sources:"}
          </p>
          
          <div className="space-y-2">
            {downloadLinks.map((link, index) => (
              <Button
                key={index}
                variant="outline"
                size="sm"
                className="w-full justify-between"
                onClick={() => window.open(link.url, '_blank')}
              >
                <span>{link.name}</span>
                <ExternalLink className="h-4 w-4" />
              </Button>
            ))}
          </div>
        </div>

        <div className="border-t pt-3">
          <p className="text-xs text-gray-500">
            {language === "ar" 
              ? "يمكنك أيضاً استخدام وضع الآيات للقراءة"
              : "You can also use verse mode for reading"
            }
          </p>
        </div>
      </div>
    </Card>
  );
}
