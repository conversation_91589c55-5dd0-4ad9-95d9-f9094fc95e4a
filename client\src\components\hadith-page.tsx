import { useState } from "react";
import { Card } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Ta<PERSON>, Ta<PERSON><PERSON>ontent, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { hadithData, hadithCategories, hadithSources } from "@/data/hadith-data";
import { ArrowLeft, BookOpen, Copy, Share2, Search, Filter } from "lucide-react";
import { useToast } from "@/hooks/use-toast";

interface HadithPageProps {
  onBack: () => void;
  language: "en" | "ar";
}

export function HadithPage({ onBack, language }: HadithPageProps) {
  const [selectedCategory, setSelectedCategory] = useState("faith");
  const [selectedSource, setSelectedSource] = useState("all");
  const [selectedGrade, setSelectedGrade] = useState("all");
  const { toast } = useToast();

  const filteredHadith = hadithData.filter(hadith => {
    const categoryMatch = hadith.category === selectedCategory;
    const sourceMatch = selectedSource === "all" || hadith.source.includes(selectedSource);
    const gradeMatch = selectedGrade === "all" || hadith.grade === selectedGrade;
    return categoryMatch && sourceMatch && gradeMatch;
  });

  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text);
    toast({
      title: language === "ar" ? "تم النسخ" : "Copied",
      description: language === "ar" ? "تم نسخ النص" : "Text copied to clipboard"
    });
  };

  const getGradeBadgeColor = (grade: string) => {
    switch (grade) {
      case "sahih":
        return "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200";
      case "hasan":
        return "bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200";
      case "daif":
        return "bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200";
      default:
        return "bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200";
    }
  };

  return (
    <div className="min-h-screen bg-background">
      {/* Header */}
      <header className="bg-primary text-primary-foreground p-4 flex items-center">
        <Button variant="ghost" size="sm" onClick={onBack} className="mr-3 text-primary-foreground hover:bg-primary-foreground/20">
          <ArrowLeft className="h-5 w-5" />
        </Button>
        <div className="flex items-center space-x-3">
          <BookOpen className="h-6 w-6" />
          <h1 className="text-xl font-bold">
            {language === "ar" ? "الأحاديث النبوية" : "Prophetic Traditions"}
          </h1>
        </div>
      </header>

      <div className="p-4">
        {/* Filters */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-3 mb-6">
          <Select value={selectedSource} onValueChange={setSelectedSource}>
            <SelectTrigger>
              <SelectValue placeholder={language === "ar" ? "المصدر" : "Source"} />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">
                {language === "ar" ? "جميع المصادر" : "All Sources"}
              </SelectItem>
              {hadithSources.map((source) => (
                <SelectItem key={source.id} value={source.id}>
                  {language === "ar" ? source.name : source.englishName}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>

          <Select value={selectedGrade} onValueChange={setSelectedGrade}>
            <SelectTrigger>
              <SelectValue placeholder={language === "ar" ? "الدرجة" : "Grade"} />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">
                {language === "ar" ? "جميع الدرجات" : "All Grades"}
              </SelectItem>
              <SelectItem value="sahih">
                {language === "ar" ? "صحيح" : "Sahih"}
              </SelectItem>
              <SelectItem value="hasan">
                {language === "ar" ? "حسن" : "Hasan"}
              </SelectItem>
              <SelectItem value="daif">
                {language === "ar" ? "ضعيف" : "Daif"}
              </SelectItem>
            </SelectContent>
          </Select>

          <div className="flex items-center justify-center">
            <Badge variant="outline">
              {filteredHadith.length} {language === "ar" ? "حديث" : "hadith"}
            </Badge>
          </div>
        </div>

        {/* Category Tabs */}
        <Tabs value={selectedCategory} onValueChange={setSelectedCategory} className="w-full">
          <TabsList className="grid w-full grid-cols-2 lg:grid-cols-4 mb-6">
            {hadithCategories.slice(0, 8).map((category) => (
              <TabsTrigger
                key={category.id}
                value={category.id}
                className="text-xs px-2 py-2"
              >
                {language === "ar" ? category.name : category.englishName}
              </TabsTrigger>
            ))}
          </TabsList>

          {hadithCategories.map((category) => (
            <TabsContent key={category.id} value={category.id}>
              <ScrollArea className="h-[calc(100vh-250px)]">
                <div className="space-y-6">
                  {filteredHadith.map((hadith) => (
                    <Card key={hadith.id} className="p-6">
                      {/* Header with Narrator and Source */}
                      <div className="flex items-center justify-between mb-4">
                        <div className="flex items-center space-x-2">
                          <Badge variant="outline">
                            {hadith.narrator}
                          </Badge>
                          <Badge className={getGradeBadgeColor(hadith.grade)}>
                            {language === "ar" 
                              ? hadith.grade === "sahih" ? "صحيح" : hadith.grade === "hasan" ? "حسن" : "ضعيف"
                              : hadith.grade.charAt(0).toUpperCase() + hadith.grade.slice(1)
                            }
                          </Badge>
                        </div>
                        <Badge variant="secondary">
                          {hadith.source}
                        </Badge>
                      </div>

                      {/* Arabic Text */}
                      <div className="mb-4 p-4 bg-muted rounded-xl rtl">
                        <p className="text-lg font-amiri leading-loose text-right text-foreground">
                          {hadith.arabic}
                        </p>
                      </div>

                      {/* Translation */}
                      <div className="mb-4 p-4 bg-blue-50 dark:bg-blue-900/30 rounded-lg">
                        <p className="text-sm text-blue-800 dark:text-blue-200 leading-relaxed">
                          {hadith.translation}
                        </p>
                      </div>

                      {/* Chapter */}
                      <div className="mb-4">
                        <span className="text-xs text-muted-foreground">
                          {language === "ar" ? "الباب: " : "Chapter: "}
                          {hadith.chapter}
                        </span>
                      </div>

                      {/* Action Buttons */}
                      <div className="flex items-center justify-between pt-3 border-t">
                        <div className="flex items-center space-x-2">
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => copyToClipboard(hadith.arabic)}
                            className="flex items-center space-x-1"
                          >
                            <Copy className="h-4 w-4" />
                            <span className="text-xs">
                              {language === "ar" ? "نسخ" : "Copy"}
                            </span>
                          </Button>

                          <Button
                            variant="ghost"
                            size="sm"
                            className="flex items-center space-x-1"
                          >
                            <Share2 className="h-4 w-4" />
                            <span className="text-xs">
                              {language === "ar" ? "مشاركة" : "Share"}
                            </span>
                          </Button>
                        </div>

                        <div className="text-xs text-muted-foreground">
                          {language === "ar" ? `${hadith.category} - ${hadith.grade}` : `${hadith.category} - ${hadith.grade}`}
                        </div>
                      </div>
                    </Card>
                  ))}

                  {filteredHadith.length === 0 && (
                    <Card className="p-8 text-center">
                      <p className="text-muted-foreground">
                        {language === "ar" 
                          ? "لا توجد أحاديث تطابق الفلاتر المحددة" 
                          : "No hadith found matching the selected filters"
                        }
                      </p>
                    </Card>
                  )}
                </div>
              </ScrollArea>
            </TabsContent>
          ))}
        </Tabs>
      </div>
    </div>
  );
}