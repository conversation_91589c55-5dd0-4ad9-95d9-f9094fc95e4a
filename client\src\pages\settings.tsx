import { useState } from "react";
import { Card } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Switch } from "@/components/ui/switch";
import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";
import { Separator } from "@/components/ui/separator";
import { Badge } from "@/components/ui/badge";
import { useTheme } from "@/components/theme-provider";
import { themes } from "@/data/themes";
import { ArrowLeft, Moon, Sun, Languages, MapPin, Volume2, Bell, Palette, User, Info } from "lucide-react";

interface SettingsProps {
  onBack: () => void;
  language: "en" | "ar";
  setLanguage: (lang: "en" | "ar") => void;
}

export default function Settings({ onBack, language, setLanguage }: SettingsProps) {
  const { theme, toggleTheme } = useTheme();
  const [currentTheme, setCurrentTheme] = useState("emerald");
  const [notifications, setNotifications] = useState({
    prayers: true,
    adhkar: true,
    quran: false
  });
  const [audioSettings, setAudioSettings] = useState({
    reciter: "mishary",
    volume: 75,
    autoPlay: false
  });
  const [locationSettings, setLocationSettings] = useState({
    autoDetect: true,
    city: "Riyadh",
    country: "Saudi Arabia"
  });
  const [calculationMethod, setCalculationMethod] = useState("MuslimWorldLeague");

  const applyTheme = (themeId: string) => {
    const selectedTheme = themes.find(t => t.id === themeId);
    if (!selectedTheme) return;

    const root = document.documentElement;
    const colors = theme === "dark" ? selectedTheme.darkColors : selectedTheme.colors;
    
    Object.entries(colors).forEach(([key, value]) => {
      root.style.setProperty(`--${key.replace(/([A-Z])/g, '-$1').toLowerCase()}`, value);
    });
    
    setCurrentTheme(themeId);
  };

  const calculationMethods = [
    { id: "MuslimWorldLeague", name: "Muslim World League", arabicName: "رابطة العالم الإسلامي" },
    { id: "Egyptian", name: "Egyptian General Authority", arabicName: "الهيئة المصرية العامة للمساحة" },
    { id: "Karachi", name: "University of Karachi", arabicName: "جامعة كراتشي" },
    { id: "UmmAlQura", name: "Umm Al-Qura University", arabicName: "جامعة أم القرى" },
    { id: "Dubai", name: "Dubai", arabicName: "دبي" },
    { id: "MoonsightingCommittee", name: "Moonsighting Committee", arabicName: "لجنة مراقبة الهلال" }
  ];

  return (
    <div className="min-h-screen bg-background">
      {/* Header */}
      <header className="bg-primary text-primary-foreground p-4 flex items-center">
        <Button variant="ghost" size="sm" onClick={onBack} className="mr-3 text-primary-foreground hover:bg-primary-foreground/20">
          <ArrowLeft className="h-5 w-5" />
        </Button>
        <h1 className="text-xl font-bold">
          {language === "ar" ? "الإعدادات" : "Settings"}
        </h1>
      </header>

      <div className="p-4 space-y-4">
        {/* Theme Settings */}
        <Card className="p-6">
          <div className="flex items-center space-x-3 mb-4">
            <Palette className="h-5 w-5 text-primary" />
            <h2 className="text-lg font-semibold">
              {language === "ar" ? "إعدادات المظهر" : "Appearance Settings"}
            </h2>
          </div>
          
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <Label htmlFor="dark-mode" className="flex items-center space-x-2">
                {theme === "dark" ? <Moon className="h-4 w-4" /> : <Sun className="h-4 w-4" />}
                <span>{language === "ar" ? "الوضع المظلم" : "Dark Mode"}</span>
              </Label>
              <Switch
                id="dark-mode"
                checked={theme === "dark"}
                onCheckedChange={toggleTheme}
              />
            </div>

            <div className="space-y-2">
              <Label>{language === "ar" ? "اختر السمة" : "Choose Theme"}</Label>
              <div className="grid grid-cols-2 gap-3">
                {themes.map((themeOption) => (
                  <Button
                    key={themeOption.id}
                    variant={currentTheme === themeOption.id ? "default" : "outline"}
                    className="h-auto p-3 flex flex-col items-center space-y-2"
                    onClick={() => applyTheme(themeOption.id)}
                  >
                    <div 
                      className="w-8 h-8 rounded-full"
                      style={{ backgroundColor: themeOption.colors.primary }}
                    ></div>
                    <span className="text-xs text-center">
                      {language === "ar" ? themeOption.arabicName : themeOption.name}
                    </span>
                  </Button>
                ))}
              </div>
            </div>

            <div className="flex items-center justify-between">
              <Label htmlFor="language" className="flex items-center space-x-2">
                <Languages className="h-4 w-4" />
                <span>{language === "ar" ? "اللغة" : "Language"}</span>
              </Label>
              <Select value={language} onValueChange={(value: "en" | "ar") => setLanguage(value)}>
                <SelectTrigger className="w-32">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="ar">العربية</SelectItem>
                  <SelectItem value="en">English</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
        </Card>

        {/* Prayer Settings */}
        <Card className="p-6">
          <div className="flex items-center space-x-3 mb-4">
            <User className="h-5 w-5 text-primary" />
            <h2 className="text-lg font-semibold">
              {language === "ar" ? "إعدادات الصلاة" : "Prayer Settings"}
            </h2>
          </div>
          
          <div className="space-y-4">
            <div className="space-y-2">
              <Label>{language === "ar" ? "طريقة الحساب" : "Calculation Method"}</Label>
              <Select value={calculationMethod} onValueChange={setCalculationMethod}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  {calculationMethods.map((method) => (
                    <SelectItem key={method.id} value={method.id}>
                      {language === "ar" ? method.arabicName : method.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>
        </Card>

        {/* Location Settings */}
        <Card className="p-6">
          <div className="flex items-center space-x-3 mb-4">
            <MapPin className="h-5 w-5 text-primary" />
            <h2 className="text-lg font-semibold">
              {language === "ar" ? "إعدادات الموقع" : "Location Settings"}
            </h2>
          </div>
          
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <Label htmlFor="auto-location">
                {language === "ar" ? "تحديد الموقع تلقائياً" : "Auto-detect Location"}
              </Label>
              <Switch
                id="auto-location"
                checked={locationSettings.autoDetect}
                onCheckedChange={(checked) => 
                  setLocationSettings(prev => ({ ...prev, autoDetect: checked }))
                }
              />
            </div>

            {!locationSettings.autoDetect && (
              <div className="space-y-2">
                <div>
                  <Label>{language === "ar" ? "المدينة" : "City"}</Label>
                  <Input
                    value={locationSettings.city}
                    onChange={(e) => 
                      setLocationSettings(prev => ({ ...prev, city: e.target.value }))
                    }
                    placeholder={language === "ar" ? "أدخل المدينة" : "Enter city"}
                  />
                </div>
                <div>
                  <Label>{language === "ar" ? "البلد" : "Country"}</Label>
                  <Input
                    value={locationSettings.country}
                    onChange={(e) => 
                      setLocationSettings(prev => ({ ...prev, country: e.target.value }))
                    }
                    placeholder={language === "ar" ? "أدخل البلد" : "Enter country"}
                  />
                </div>
              </div>
            )}
          </div>
        </Card>

        {/* Notification Settings */}
        <Card className="p-6">
          <div className="flex items-center space-x-3 mb-4">
            <Bell className="h-5 w-5 text-primary" />
            <h2 className="text-lg font-semibold">
              {language === "ar" ? "إعدادات الإشعارات" : "Notification Settings"}
            </h2>
          </div>
          
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <Label htmlFor="prayer-notifications">
                {language === "ar" ? "إشعارات الصلاة" : "Prayer Notifications"}
              </Label>
              <Switch
                id="prayer-notifications"
                checked={notifications.prayers}
                onCheckedChange={(checked) => 
                  setNotifications(prev => ({ ...prev, prayers: checked }))
                }
              />
            </div>

            <div className="flex items-center justify-between">
              <Label htmlFor="adhkar-notifications">
                {language === "ar" ? "إشعارات الأذكار" : "Adhkar Notifications"}
              </Label>
              <Switch
                id="adhkar-notifications"
                checked={notifications.adhkar}
                onCheckedChange={(checked) => 
                  setNotifications(prev => ({ ...prev, adhkar: checked }))
                }
              />
            </div>

            <div className="flex items-center justify-between">
              <Label htmlFor="quran-notifications">
                {language === "ar" ? "تذكير قراءة القرآن" : "Quran Reading Reminders"}
              </Label>
              <Switch
                id="quran-notifications"
                checked={notifications.quran}
                onCheckedChange={(checked) => 
                  setNotifications(prev => ({ ...prev, quran: checked }))
                }
              />
            </div>
          </div>
        </Card>

        {/* Audio Settings */}
        <Card className="p-6">
          <div className="flex items-center space-x-3 mb-4">
            <Volume2 className="h-5 w-5 text-primary" />
            <h2 className="text-lg font-semibold">
              {language === "ar" ? "إعدادات الصوت" : "Audio Settings"}
            </h2>
          </div>
          
          <div className="space-y-4">
            <div className="space-y-2">
              <Label>{language === "ar" ? "القارئ المفضل" : "Preferred Reciter"}</Label>
              <Select value={audioSettings.reciter} onValueChange={(value) => 
                setAudioSettings(prev => ({ ...prev, reciter: value }))
              }>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="mishary">مشاري راشد العفاسي</SelectItem>
                  <SelectItem value="sudais">عبد الرحمن السديس</SelectItem>
                  <SelectItem value="ghamdi">سعد الغامدي</SelectItem>
                  <SelectItem value="shuraim">سعود الشريم</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label>{language === "ar" ? "مستوى الصوت" : "Volume Level"}</Label>
              <div className="flex items-center space-x-3">
                <Input
                  type="range"
                  min="0"
                  max="100"
                  value={audioSettings.volume}
                  onChange={(e) => 
                    setAudioSettings(prev => ({ ...prev, volume: parseInt(e.target.value) }))
                  }
                  className="flex-1"
                />
                <span className="w-12 text-sm">{audioSettings.volume}%</span>
              </div>
            </div>

            <div className="flex items-center justify-between">
              <Label htmlFor="auto-play">
                {language === "ar" ? "التشغيل التلقائي" : "Auto Play"}
              </Label>
              <Switch
                id="auto-play"
                checked={audioSettings.autoPlay}
                onCheckedChange={(checked) => 
                  setAudioSettings(prev => ({ ...prev, autoPlay: checked }))
                }
              />
            </div>
          </div>
        </Card>

        {/* App Info */}
        <Card className="p-6">
          <div className="flex items-center space-x-3 mb-4">
            <Info className="h-5 w-5 text-primary" />
            <h2 className="text-lg font-semibold">
              {language === "ar" ? "معلومات التطبيق" : "App Information"}
            </h2>
          </div>
          
          <div className="space-y-3">
            <div className="flex justify-between items-center">
              <span className="text-sm text-muted-foreground">
                {language === "ar" ? "الإصدار" : "Version"}
              </span>
              <Badge variant="outline">1.0.0</Badge>
            </div>
            
            <div className="flex justify-between items-center">
              <span className="text-sm text-muted-foreground">
                {language === "ar" ? "آخر تحديث" : "Last Update"}
              </span>
              <span className="text-sm">
                {language === "ar" ? "18 يونيو 2025" : "June 18, 2025"}
              </span>
            </div>

            <Separator />
            
            <div className="text-center pt-2">
              <p className="text-xs text-muted-foreground">
                {language === "ar" 
                  ? "تطبيق إسلامي شامل للمسلمين في جميع أنحاء العالم" 
                  : "Comprehensive Islamic app for Muslims worldwide"
                }
              </p>
            </div>
          </div>
        </Card>

        {/* Save Button */}
        <Button className="w-full bg-primary hover:bg-primary/90 text-primary-foreground py-3">
          {language === "ar" ? "حفظ الإعدادات" : "Save Settings"}
        </Button>
      </div>
    </div>
  );
}