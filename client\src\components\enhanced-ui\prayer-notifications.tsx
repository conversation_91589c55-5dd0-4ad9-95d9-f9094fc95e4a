import { useEffect, useState } from 'react';
import { Bell, BellOff, Volume2, VolumeX } from 'lucide-react';
import { Button } from '../ui/button';
import { Card } from '../ui/card';
import { Switch } from '../ui/switch';
import { getPrayerTimes } from '../../lib/prayer-times';
import { useLocation } from '../../hooks/use-location';
import { hapticFeedback, audioFeedback } from './haptic-feedback';

interface NotificationSettings {
  enabled: boolean;
  sound: boolean;
  vibration: boolean;
  beforeMinutes: number;
}

export function PrayerNotifications() {
  const [settings, setSettings] = useState<NotificationSettings>({
    enabled: false,
    sound: true,
    vibration: true,
    beforeMinutes: 10
  });
  const [permission, setPermission] = useState<NotificationPermission>('default');
  const { location } = useLocation();

  useEffect(() => {
    if ('Notification' in window) {
      setPermission(Notification.permission);
    }
  }, []);

  const requestPermission = async () => {
    if ('Notification' in window) {
      const result = await Notification.requestPermission();
      setPermission(result);
      if (result === 'granted') {
        setSettings(prev => ({ ...prev, enabled: true }));
      }
    }
  };

  const scheduleNotifications = () => {
    if (!location || !settings.enabled) return;

    const prayerTimes = getPrayerTimes(location.latitude, location.longitude);
    const prayers = [
      { name: 'الفجر', time: prayerTimes.fajr, arabic: 'Fajr' },
      { name: 'الظهر', time: prayerTimes.dhuhr, arabic: 'Dhuhr' },
      { name: 'العصر', time: prayerTimes.asr, arabic: 'Asr' },
      { name: 'المغرب', time: prayerTimes.maghrib, arabic: 'Maghrib' },
      { name: 'العشاء', time: prayerTimes.isha, arabic: 'Isha' }
    ];

    prayers.forEach(prayer => {
      const notificationTime = new Date(prayer.time.getTime() - (settings.beforeMinutes * 60000));
      const now = new Date();

      if (notificationTime > now) {
        const timeUntilNotification = notificationTime.getTime() - now.getTime();
        
        setTimeout(() => {
          if (settings.sound) {
            audioFeedback.playNotification();
          }
          if (settings.vibration) {
            hapticFeedback.medium();
          }
          
          new Notification(`حان وقت صلاة ${prayer.name}`, {
            body: `ستبدأ صلاة ${prayer.name} خلال ${settings.beforeMinutes} دقائق`,
            icon: '/icon-192x192.png',
            tag: prayer.arabic,
            requireInteraction: true
          });
        }, timeUntilNotification);
      }
    });
  };

  useEffect(() => {
    if (settings.enabled && permission === 'granted') {
      scheduleNotifications();
    }
  }, [settings.enabled, permission, location]);

  return (
    <Card className="p-4 space-y-4">
      <div className="flex items-center justify-between">
        <h3 className="text-lg font-semibold flex items-center gap-2">
          <Bell className="h-5 w-5" />
          إشعارات الصلاة
        </h3>
        <Switch
          checked={settings.enabled}
          onCheckedChange={(enabled) => {
            if (enabled && permission !== 'granted') {
              requestPermission();
            } else {
              setSettings(prev => ({ ...prev, enabled }));
            }
          }}
        />
      </div>

      {permission === 'denied' && (
        <div className="bg-red-50 dark:bg-red-900/20 p-3 rounded-lg text-sm text-red-800 dark:text-red-200">
          تم رفض إذن الإشعارات. يرجى تفعيلها من إعدادات المتصفح.
        </div>
      )}

      {settings.enabled && (
        <div className="space-y-3">
          <div className="flex items-center justify-between">
            <span className="text-sm flex items-center gap-2">
              <Volume2 className="h-4 w-4" />
              الصوت
            </span>
            <Switch
              checked={settings.sound}
              onCheckedChange={(sound) => setSettings(prev => ({ ...prev, sound }))}
            />
          </div>

          <div className="flex items-center justify-between">
            <span className="text-sm">الاهتزاز</span>
            <Switch
              checked={settings.vibration}
              onCheckedChange={(vibration) => setSettings(prev => ({ ...prev, vibration }))}
            />
          </div>

          <div className="flex items-center justify-between">
            <span className="text-sm">التنبيه قبل الأذان</span>
            <select
              value={settings.beforeMinutes}
              onChange={(e) => setSettings(prev => ({ ...prev, beforeMinutes: parseInt(e.target.value) }))}
              className="text-sm border rounded px-2 py-1 bg-background"
            >
              <option value={5}>5 دقائق</option>
              <option value={10}>10 دقائق</option>
              <option value={15}>15 دقيقة</option>
              <option value={30}>30 دقيقة</option>
            </select>
          </div>
        </div>
      )}
    </Card>
  );
}