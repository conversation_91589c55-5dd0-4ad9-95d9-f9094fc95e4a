import { useState } from "react";
import { Card } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Switch } from "@/components/ui/switch";
import { Label } from "@/components/ui/label";
import { Slider } from "@/components/ui/slider";
import { Input } from "@/components/ui/input";
import { Separator } from "@/components/ui/separator";
import { ScrollArea } from "@/components/ui/scroll-area";
import { X, Volume2, Eye, Languages, Bookmark, Play, Pause, SkipBack, SkipForward, Download, Share2 } from "lucide-react";
import { reciters } from "@/data/quran-data";

interface QuranPageSidebarProps {
  isOpen: boolean;
  onClose: () => void;
  language: "en" | "ar";
  currentPage: number;
  showTranslation: boolean;
  setShowTranslation: (show: boolean) => void;
  showTafsir: boolean;
  setShowTafsir: (show: boolean) => void;
  audioPlaying: boolean;
  setAudioPlaying: (playing: boolean) => void;
  selectedReciter: string;
  setSelectedReciter: (reciter: string) => void;
  audioVolume: number;
  setAudioVolume: (volume: number) => void;
  onPageChange: (page: number) => void;
  onBookmark: () => void;
  onShare: () => void;
  onDownload: () => void;
}

export function QuranPageSidebar({
  isOpen,
  onClose,
  language,
  currentPage,
  showTranslation,
  setShowTranslation,
  showTafsir,
  setShowTafsir,
  audioPlaying,
  setAudioPlaying,
  selectedReciter,
  setSelectedReciter,
  audioVolume,
  setAudioVolume,
  onPageChange,
  onBookmark,
  onShare,
  onDownload
}: QuranPageSidebarProps) {
  const [searchSurah, setSearchSurah] = useState("");
  const [searchAyah, setSearchAyah] = useState("");

  const quickNavigation = [
    { name: language === "ar" ? "الفاتحة" : "Al-Fatiha", page: 1 },
    { name: language === "ar" ? "البقرة" : "Al-Baqarah", page: 2 },
    { name: language === "ar" ? "آل عمران" : "Al Imran", page: 50 },
    { name: language === "ar" ? "النساء" : "An-Nisa", page: 77 },
    { name: language === "ar" ? "المائدة" : "Al-Ma'idah", page: 106 },
    { name: language === "ar" ? "الأنعام" : "Al-An'am", page: 128 },
    { name: language === "ar" ? "الكهف" : "Al-Kahf", page: 293 },
    { name: language === "ar" ? "يس" : "Ya-Sin", page: 362 },
    { name: language === "ar" ? "الملك" : "Al-Mulk", page: 562 },
    { name: language === "ar" ? "عم" : "Amma", page: 582 }
  ];

  if (!isOpen) return null;

  return (
    <>
      {/* Backdrop */}
      <div 
        className="fixed inset-0 bg-black bg-opacity-50 z-40"
        onClick={onClose}
      />
      
      {/* Sidebar */}
      <div className="fixed top-0 right-0 h-full w-80 bg-white dark:bg-gray-900 shadow-xl z-50 transform transition-transform duration-300">
        <div className="flex flex-col h-full">
          {/* Header */}
          <div className="flex items-center justify-between p-4 border-b">
            <h2 className="text-lg font-semibold">
              {language === "ar" ? "إعدادات المصحف" : "Quran Settings"}
            </h2>
            <Button variant="ghost" size="sm" onClick={onClose}>
              <X className="h-5 w-5" />
            </Button>
          </div>

          <ScrollArea className="flex-1 p-4">
            {/* Audio Controls */}
            <Card className="p-4 mb-4">
              <div className="flex items-center space-x-2 mb-3">
                <Volume2 className="h-4 w-4" />
                <h3 className="font-medium">
                  {language === "ar" ? "التلاوة الصوتية" : "Audio Recitation"}
                </h3>
              </div>

              <div className="space-y-3">
                <div className="flex items-center justify-center space-x-2">
                  <Button variant="outline" size="sm">
                    <SkipBack className="h-4 w-4" />
                  </Button>
                  <Button 
                    onClick={() => setAudioPlaying(!audioPlaying)}
                    className="bg-primary hover:bg-primary/90"
                  >
                    {audioPlaying ? <Pause className="h-4 w-4" /> : <Play className="h-4 w-4" />}
                  </Button>
                  <Button variant="outline" size="sm">
                    <SkipForward className="h-4 w-4" />
                  </Button>
                </div>

                <div>
                  <Label className="text-sm">
                    {language === "ar" ? "القارئ" : "Reciter"}
                  </Label>
                  <Select value={selectedReciter} onValueChange={setSelectedReciter}>
                    <SelectTrigger className="mt-1">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      {reciters.map((reciter) => (
                        <SelectItem key={reciter.id} value={reciter.id}>
                          {language === "ar" ? reciter.arabicName : reciter.name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                <div>
                  <Label className="text-sm">
                    {language === "ar" ? "مستوى الصوت" : "Volume"} ({audioVolume}%)
                  </Label>
                  <Slider
                    value={[audioVolume]}
                    onValueChange={(value) => setAudioVolume(value[0])}
                    max={100}
                    step={5}
                    className="mt-2"
                  />
                </div>
              </div>
            </Card>

            {/* Display Options */}
            <Card className="p-4 mb-4">
              <div className="flex items-center space-x-2 mb-3">
                <Eye className="h-4 w-4" />
                <h3 className="font-medium">
                  {language === "ar" ? "خيارات العرض" : "Display Options"}
                </h3>
              </div>

              <div className="space-y-3">
                <div className="flex items-center justify-between">
                  <Label htmlFor="translation">
                    {language === "ar" ? "إظهار الترجمة" : "Show Translation"}
                  </Label>
                  <Switch
                    id="translation"
                    checked={showTranslation}
                    onCheckedChange={setShowTranslation}
                  />
                </div>

                <div className="flex items-center justify-between">
                  <Label htmlFor="tafsir">
                    {language === "ar" ? "إظهار التفسير" : "Show Tafsir"}
                  </Label>
                  <Switch
                    id="tafsir"
                    checked={showTafsir}
                    onCheckedChange={setShowTafsir}
                  />
                </div>
              </div>
            </Card>

            {/* Navigation */}
            <Card className="p-4 mb-4">
              <div className="flex items-center space-x-2 mb-3">
                <Bookmark className="h-4 w-4" />
                <h3 className="font-medium">
                  {language === "ar" ? "التنقل السريع" : "Quick Navigation"}
                </h3>
              </div>

              <div className="space-y-2 mb-4">
                {quickNavigation.map((item, index) => (
                  <Button
                    key={index}
                    variant="outline"
                    size="sm"
                    className="w-full justify-start"
                    onClick={() => onPageChange(item.page)}
                  >
                    {item.name} ({item.page})
                  </Button>
                ))}
              </div>

              <Separator className="my-3" />

              <div className="space-y-2">
                <Label className="text-sm">
                  {language === "ar" ? "الانتقال لصفحة محددة" : "Go to Specific Page"}
                </Label>
                <div className="flex space-x-2">
                  <Input
                    type="number"
                    min="1"
                    max="604"
                    placeholder={language === "ar" ? "رقم الصفحة" : "Page #"}
                    className="flex-1"
                    onKeyPress={(e) => {
                      if (e.key === 'Enter') {
                        const page = parseInt((e.target as HTMLInputElement).value);
                        if (page >= 1 && page <= 604) {
                          onPageChange(page);
                          (e.target as HTMLInputElement).value = '';
                        }
                      }
                    }}
                  />
                </div>
              </div>
            </Card>

            {/* Actions */}
            <Card className="p-4">
              <h3 className="font-medium mb-3">
                {language === "ar" ? "الإجراءات" : "Actions"}
              </h3>
              
              <div className="grid grid-cols-2 gap-2">
                <Button variant="outline" size="sm" onClick={onBookmark}>
                  <Bookmark className="h-4 w-4 mr-1" />
                  {language === "ar" ? "حفظ" : "Bookmark"}
                </Button>
                <Button variant="outline" size="sm" onClick={onShare}>
                  <Share2 className="h-4 w-4 mr-1" />
                  {language === "ar" ? "مشاركة" : "Share"}
                </Button>
                <Button variant="outline" size="sm" onClick={onDownload} className="col-span-2">
                  <Download className="h-4 w-4 mr-1" />
                  {language === "ar" ? "تحميل الصفحة" : "Download Page"}
                </Button>
              </div>
            </Card>
          </ScrollArea>
        </div>
      </div>
    </>
  );
}
