import { useState, useEffect } from "react";
import { Card } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Badge } from "@/components/ui/badge";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { quranData, reciters } from "@/data/quran-data";
import { findPageBySurah, findPageBySurahAndAyah } from "@/data/quran-pages";
import { Play, Pause, SkipBack, SkipForward, Settings, ArrowLeft, BookOpen, Copy, Share2, FileImage, ChevronLeft, ChevronRight, ZoomIn, ZoomOut, RotateCcw, Download } from "lucide-react";
import { useToast } from "@/hooks/use-toast";

interface QuranReaderProps {
  onBack?: () => void;
  language?: "en" | "ar";
}

export function QuranReader({ onBack, language = "en" }: QuranReaderProps) {
  const [selectedSurah, setSelectedSurah] = useState<number>(1);
  const [currentAyah, setCurrentAyah] = useState<number>(1);
  const [selectedReciter, setSelectedReciter] = useState<string>("mishary");
  const [isPlaying, setIsPlaying] = useState<boolean>(false);
  const [showFullSurah, setShowFullSurah] = useState<boolean>(false);
  const [currentPage, setCurrentPage] = useState<number>(1);
  const [viewMode, setViewMode] = useState<"verses" | "pages">("verses");
  const [zoomLevel, setZoomLevel] = useState<number>(1);
  const [searchSurah, setSearchSurah] = useState<string>("");
  const [searchAyah, setSearchAyah] = useState<string>("");
  const { toast } = useToast();

  const currentSurah = quranData.find(s => s.number === selectedSurah);
  const currentVerse = currentSurah?.verses?.[currentAyah - 1];

  // Load last read page on component mount
  useEffect(() => {
    const lastPage = localStorage.getItem('quran-last-page');
    if (lastPage && viewMode === "pages") {
      setCurrentPage(parseInt(lastPage));
    }
  }, [viewMode]);

  // Save current page when it changes
  useEffect(() => {
    if (viewMode === "pages") {
      localStorage.setItem('quran-last-page', currentPage.toString());
    }
  }, [currentPage, viewMode]);

  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text);
    toast({
      title: language === "ar" ? "تم النسخ" : "Copied",
      description: language === "ar" ? "تم نسخ النص" : "Text copied to clipboard"
    });
  };

  const handlePrevious = () => {
    if (currentAyah > 1) {
      setCurrentAyah(currentAyah - 1);
    } else if (selectedSurah > 1) {
      const prevSurah = quranData.find(s => s.number === selectedSurah - 1);
      if (prevSurah) {
        setSelectedSurah(selectedSurah - 1);
        setCurrentAyah(prevSurah.verses.length);
      }
    }
  };

  const handleNext = () => {
    if (currentSurah && currentAyah < currentSurah.verses.length) {
      setCurrentAyah(currentAyah + 1);
    } else if (selectedSurah < quranData.length) {
      setSelectedSurah(selectedSurah + 1);
      setCurrentAyah(1);
    }
  };

  const togglePlayback = () => {
    setIsPlaying(!isPlaying);
  };

  const handlePreviousPage = () => {
    if (currentPage > 1) {
      setCurrentPage(currentPage - 1);
    }
  };

  const handleNextPage = () => {
    if (currentPage < 604) { // Total pages in Quran
      setCurrentPage(currentPage + 1);
    }
  };

  const getPageImageUrl = (pageNumber: number) => {
    return `https://quran.ksu.edu.sa/png_big/${pageNumber}.png`;
  };

  const handleZoomIn = () => {
    setZoomLevel(prev => Math.min(prev + 0.2, 3));
  };

  const handleZoomOut = () => {
    setZoomLevel(prev => Math.max(prev - 0.2, 0.5));
  };

  const resetZoom = () => {
    setZoomLevel(1);
  };

  const handleSearchGo = () => {
    const surahNum = parseInt(searchSurah);
    const ayahNum = parseInt(searchAyah);

    if (surahNum >= 1 && surahNum <= 114) {
      let targetPage;
      if (ayahNum && ayahNum > 0) {
        targetPage = findPageBySurahAndAyah(surahNum, ayahNum);
      } else {
        targetPage = findPageBySurah(surahNum);
      }
      setCurrentPage(targetPage);
      toast({
        title: language === "ar" ? "تم الانتقال" : "Navigated",
        description: language === "ar"
          ? `تم الانتقال إلى الصفحة ${targetPage}`
          : `Navigated to page ${targetPage}`
      });
    } else {
      toast({
        title: language === "ar" ? "خطأ" : "Error",
        description: language === "ar"
          ? "رقم السورة غير صحيح"
          : "Invalid surah number",
        variant: "destructive"
      });
    }
  };

  if (showFullSurah) {
    return (
      <div className="min-h-screen bg-background">
        {/* Header */}
        <header className="bg-primary text-primary-foreground p-4 flex items-center">
          <Button variant="ghost" size="sm" onClick={() => setShowFullSurah(false)} className="mr-3 text-primary-foreground hover:bg-primary-foreground/20">
            <ArrowLeft className="h-5 w-5" />
          </Button>
          <div className="flex items-center space-x-3">
            <BookOpen className="h-6 w-6" />
            <div>
              <h1 className="text-xl font-bold">
                {currentSurah?.name} - {currentSurah?.englishName}
              </h1>
              <p className="text-sm opacity-90">
                {currentSurah?.meaning} • {currentSurah?.type} • {currentSurah?.verses.length} {language === "ar" ? "آية" : "verses"}
              </p>
            </div>
          </div>
        </header>

        <ScrollArea className="h-[calc(100vh-80px)] p-4">
          <div className="space-y-6">
            {currentSurah?.verses.map((verse, index) => (
              <Card key={verse.number} className="p-6">
                <div className="flex items-center justify-between mb-4">
                  <Badge className="bg-primary text-primary-foreground">
                    {language === "ar" ? `آية ${verse.number}` : `Verse ${verse.number}`}
                  </Badge>
                  <div className="flex items-center space-x-2">
                    <Button variant="ghost" size="sm" onClick={() => copyToClipboard(verse.arabic)}>
                      <Copy className="h-4 w-4" />
                    </Button>
                    <Button variant="ghost" size="sm">
                      <Share2 className="h-4 w-4" />
                    </Button>
                  </div>
                </div>

                {/* Arabic Text */}
                <div className="mb-4 p-4 bg-muted rounded-xl rtl">
                  <p className="text-xl font-amiri leading-loose text-right text-foreground">
                    {verse.arabic}
                  </p>
                </div>

                {/* Transliteration */}
                <div className="mb-4 p-3 bg-blue-50 dark:bg-blue-900/30 rounded-lg">
                  <p className="text-sm font-medium text-blue-800 dark:text-blue-200 italic">
                    {verse.transliteration}
                  </p>
                </div>

                {/* Translation */}
                <div className="p-3 bg-green-50 dark:bg-green-900/30 rounded-lg">
                  <p className="text-sm text-green-800 dark:text-green-200 leading-relaxed">
                    {verse.translation}
                  </p>
                </div>
              </Card>
            ))}
          </div>
        </ScrollArea>
      </div>
    );
  }

  // Page View Component
  const PageView = () => (
    <div className="min-h-screen bg-background">
      {/* Header */}
      <header className="bg-primary text-primary-foreground p-4 flex items-center justify-between">
        <Button variant="ghost" size="sm" onClick={() => setViewMode("verses")} className="text-primary-foreground hover:bg-primary-foreground/20">
          <ArrowLeft className="h-5 w-5" />
        </Button>
        <div className="flex items-center space-x-3">
          <FileImage className="h-6 w-6" />
          <div className="text-center">
            <h1 className="text-xl font-bold">
              {language === "ar" ? "المصحف الشريف" : "Quran Pages"}
            </h1>
            <p className="text-sm opacity-90">
              {language === "ar" ? `صفحة ${currentPage} من 604` : `Page ${currentPage} of 604`}
            </p>
          </div>
        </div>
        <Button
          variant="ghost"
          size="sm"
          onClick={() => {
            if (navigator.share) {
              navigator.share({
                title: `${language === "ar" ? "صفحة" : "Page"} ${currentPage} - ${language === "ar" ? "القرآن الكريم" : "Holy Quran"}`,
                text: `${language === "ar" ? "صفحة" : "Page"} ${currentPage} ${language === "ar" ? "من القرآن الكريم" : "from the Holy Quran"}`,
                url: getPageImageUrl(currentPage)
              });
            } else {
              copyToClipboard(getPageImageUrl(currentPage));
            }
          }}
          className="text-primary-foreground hover:bg-primary-foreground/20"
        >
          <Share2 className="h-5 w-5" />
        </Button>
      </header>

      {/* Page Content */}
      <div className="flex flex-col items-center p-4 quran-page-container min-h-[calc(100vh-80px)]">
        {/* Page Image */}
        <div className="relative w-full max-w-md mx-auto mb-4 overflow-hidden rounded-lg">
          <div
            className="transition-transform duration-200 ease-in-out"
            style={{ transform: `scale(${zoomLevel})` }}
          >
            <img
              src={getPageImageUrl(currentPage)}
              alt={`Quran Page ${currentPage}`}
              className="w-full h-auto quran-page-image bg-white"
              style={{ aspectRatio: "3/4" }}
              onError={(e) => {
                const target = e.target as HTMLImageElement;
                target.style.display = 'none';
              }}
              onLoad={(e) => {
                const target = e.target as HTMLImageElement;
                target.style.display = 'block';
                const loader = target.parentElement?.querySelector('.loading-indicator');
                if (loader) loader.remove();
              }}
              loading="lazy"
            />
          </div>

          {/* Loading indicator */}
          <div className="loading-indicator absolute inset-0 flex items-center justify-center bg-gray-100 dark:bg-gray-800 rounded-lg">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
          </div>

          {/* Zoom Controls */}
          <div className="absolute top-4 right-4 flex flex-col space-y-2">
            <Button
              variant="secondary"
              size="sm"
              onClick={handleZoomIn}
              className="w-10 h-10 p-0 bg-white/90 hover:bg-white shadow-md"
              title={language === "ar" ? "تكبير" : "Zoom In"}
            >
              <ZoomIn className="h-4 w-4" />
            </Button>
            <Button
              variant="secondary"
              size="sm"
              onClick={handleZoomOut}
              className="w-10 h-10 p-0 bg-white/90 hover:bg-white shadow-md"
              title={language === "ar" ? "تصغير" : "Zoom Out"}
            >
              <ZoomOut className="h-4 w-4" />
            </Button>
            <Button
              variant="secondary"
              size="sm"
              onClick={resetZoom}
              className="w-10 h-10 p-0 bg-white/90 hover:bg-white shadow-md"
              title={language === "ar" ? "إعادة تعيين" : "Reset Zoom"}
            >
              <RotateCcw className="h-4 w-4" />
            </Button>
            <Button
              variant="secondary"
              size="sm"
              onClick={() => {
                const link = document.createElement('a');
                link.href = getPageImageUrl(currentPage);
                link.download = `quran-page-${currentPage}.png`;
                link.click();
              }}
              className="w-10 h-10 p-0 bg-white/90 hover:bg-white shadow-md"
              title={language === "ar" ? "تحميل الصفحة" : "Download Page"}
            >
              <Download className="h-4 w-4" />
            </Button>
          </div>
        </div>

        {/* Navigation Controls */}
        <div className="flex items-center justify-between w-full max-w-md mx-auto mb-4">
          <Button
            variant="outline"
            size="lg"
            onClick={handlePreviousPage}
            disabled={currentPage <= 1}
            className="flex items-center space-x-2"
          >
            <ChevronLeft className="h-5 w-5" />
            <span>{language === "ar" ? "السابق" : "Previous"}</span>
          </Button>

          <div className="flex items-center space-x-2">
            <input
              type="number"
              min="1"
              max="604"
              value={currentPage}
              onChange={(e) => {
                const page = parseInt(e.target.value);
                if (page >= 1 && page <= 604) {
                  setCurrentPage(page);
                }
              }}
              className="w-16 px-2 py-1 text-center border rounded"
            />
            <span className="text-sm text-gray-600 dark:text-gray-400">/ 604</span>
          </div>

          <Button
            variant="outline"
            size="lg"
            onClick={handleNextPage}
            disabled={currentPage >= 604}
            className="flex items-center space-x-2"
          >
            <span>{language === "ar" ? "التالي" : "Next"}</span>
            <ChevronRight className="h-5 w-5" />
          </Button>
        </div>

        {/* Search by Surah/Ayah */}
        <div className="w-full max-w-md mx-auto mb-4">
          <Card className="p-4">
            <h3 className="text-sm font-semibold mb-3 text-center">
              {language === "ar" ? "البحث بالسورة والآية" : "Search by Surah & Ayah"}
            </h3>
            <div className="flex items-center space-x-2 mb-3">
              <div className="flex-1">
                <label className="text-xs text-gray-600 dark:text-gray-400">
                  {language === "ar" ? "السورة" : "Surah"}
                </label>
                <input
                  type="number"
                  min="1"
                  max="114"
                  value={searchSurah}
                  onChange={(e) => setSearchSurah(e.target.value)}
                  placeholder={language === "ar" ? "رقم السورة" : "Surah #"}
                  className="w-full px-2 py-1 text-sm border rounded"
                />
              </div>
              <div className="flex-1">
                <label className="text-xs text-gray-600 dark:text-gray-400">
                  {language === "ar" ? "الآية (اختياري)" : "Ayah (optional)"}
                </label>
                <input
                  type="number"
                  min="1"
                  value={searchAyah}
                  onChange={(e) => setSearchAyah(e.target.value)}
                  placeholder={language === "ar" ? "رقم الآية" : "Ayah #"}
                  className="w-full px-2 py-1 text-sm border rounded"
                />
              </div>
              <Button
                onClick={handleSearchGo}
                size="sm"
                className="mt-4 bg-primary hover:bg-primary/90"
              >
                {language === "ar" ? "اذهب" : "Go"}
              </Button>
            </div>
          </Card>
        </div>

        {/* Quick Navigation */}
        <div className="w-full max-w-md mx-auto">
          <Card className="p-4">
            <h3 className="text-sm font-semibold mb-3 text-center">
              {language === "ar" ? "الانتقال السريع" : "Quick Navigation"}
            </h3>
            <div className="grid grid-cols-2 gap-2 text-xs mb-4">
              <Button variant="outline" size="sm" onClick={() => setCurrentPage(1)}>
                {language === "ar" ? "الفاتحة (1)" : "Al-Fatiha (1)"}
              </Button>
              <Button variant="outline" size="sm" onClick={() => setCurrentPage(2)}>
                {language === "ar" ? "البقرة (2)" : "Al-Baqarah (2)"}
              </Button>
              <Button variant="outline" size="sm" onClick={() => setCurrentPage(50)}>
                {language === "ar" ? "آل عمران (50)" : "Al Imran (50)"}
              </Button>
              <Button variant="outline" size="sm" onClick={() => setCurrentPage(77)}>
                {language === "ar" ? "النساء (77)" : "An-Nisa (77)"}
              </Button>
              <Button variant="outline" size="sm" onClick={() => setCurrentPage(106)}>
                {language === "ar" ? "المائدة (106)" : "Al-Ma'idah (106)"}
              </Button>
              <Button variant="outline" size="sm" onClick={() => setCurrentPage(128)}>
                {language === "ar" ? "الأنعام (128)" : "Al-An'am (128)"}
              </Button>
              <Button variant="outline" size="sm" onClick={() => setCurrentPage(151)}>
                {language === "ar" ? "الأعراف (151)" : "Al-A'raf (151)"}
              </Button>
              <Button variant="outline" size="sm" onClick={() => setCurrentPage(177)}>
                {language === "ar" ? "الأنفال (177)" : "Al-Anfal (177)"}
              </Button>
              <Button variant="outline" size="sm" onClick={() => setCurrentPage(187)}>
                {language === "ar" ? "التوبة (187)" : "At-Tawbah (187)"}
              </Button>
              <Button variant="outline" size="sm" onClick={() => setCurrentPage(208)}>
                {language === "ar" ? "يونس (208)" : "Yunus (208)"}
              </Button>
              <Button variant="outline" size="sm" onClick={() => setCurrentPage(262)}>
                {language === "ar" ? "يوسف (235)" : "Yusuf (235)"}
              </Button>
              <Button variant="outline" size="sm" onClick={() => setCurrentPage(293)}>
                {language === "ar" ? "الكهف (293)" : "Al-Kahf (293)"}
              </Button>
              <Button variant="outline" size="sm" onClick={() => setCurrentPage(385)}>
                {language === "ar" ? "يس (362)" : "Ya-Sin (362)"}
              </Button>
              <Button variant="outline" size="sm" onClick={() => setCurrentPage(582)}>
                {language === "ar" ? "عم (582)" : "Amma (582)"}
              </Button>
            </div>

            {/* Bookmark Feature */}
            <div className="border-t pt-3">
              <div className="flex items-center justify-between mb-2">
                <span className="text-xs font-medium">
                  {language === "ar" ? "الصفحة المحفوظة" : "Bookmarked Page"}
                </span>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => {
                    localStorage.setItem('quran-bookmark', currentPage.toString());
                    toast({
                      title: language === "ar" ? "تم الحفظ" : "Bookmarked",
                      description: language === "ar" ? `تم حفظ الصفحة ${currentPage}` : `Page ${currentPage} bookmarked`
                    });
                  }}
                  className="h-6 px-2 text-xs"
                >
                  {language === "ar" ? "احفظ" : "Save"}
                </Button>
              </div>
              <Button
                variant="outline"
                size="sm"
                onClick={() => {
                  const bookmark = localStorage.getItem('quran-bookmark');
                  if (bookmark) {
                    setCurrentPage(parseInt(bookmark));
                  }
                }}
                className="w-full text-xs"
              >
                {language === "ar" ? "اذهب للصفحة المحفوظة" : "Go to Bookmark"}
              </Button>
            </div>
          </Card>
        </div>
      </div>
    </div>
  );

  if (viewMode === "pages") {
    return <PageView />;
  }

  return (
    <section className="p-3 sm:p-4 w-full">
      <Card className="p-4 sm:p-6 mx-auto max-w-lg">
        <div className="flex justify-between items-center mb-4 sm:mb-6">
          {onBack && (
            <Button variant="ghost" size="sm" onClick={onBack} className="mr-2 flex-shrink-0">
              <ArrowLeft className="h-4 w-4" />
            </Button>
          )}
          <h2 className="text-lg sm:text-xl font-semibold text-gray-800 dark:text-white min-w-0 flex-1 text-center">
            {language === "ar" ? "قارئ القرآن الكريم" : "Quran Reader"}
          </h2>
          <Button variant="ghost" size="sm" className="flex-shrink-0">
            <Settings className="h-4 w-4" />
          </Button>
        </div>

        {/* View Mode Tabs */}
        <Tabs value={viewMode} onValueChange={(value) => setViewMode(value as "verses" | "pages")} className="mb-4">
          <TabsList className="grid w-full grid-cols-2">
            <TabsTrigger value="verses" className="flex items-center space-x-2">
              <BookOpen className="h-4 w-4" />
              <span>{language === "ar" ? "الآيات" : "Verses"}</span>
            </TabsTrigger>
            <TabsTrigger value="pages" className="flex items-center space-x-2">
              <FileImage className="h-4 w-4" />
              <span>{language === "ar" ? "الصفحات" : "Pages"}</span>
            </TabsTrigger>
          </TabsList>
        </Tabs>

        {/* Surah Selection */}
        <div className="mb-4">
          <Select
            value={selectedSurah.toString()}
            onValueChange={(value) => {
              setSelectedSurah(parseInt(value));
              setCurrentAyah(1);
            }}
          >
            <SelectTrigger>
              <SelectValue placeholder={language === "ar" ? "اختر السورة" : "Select Surah"} />
            </SelectTrigger>
            <SelectContent>
              {quranData.map((surah) => (
                <SelectItem key={surah.number} value={surah.number.toString()}>
                  {surah.number}. {surah.name} ({surah.englishName})
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>

        {/* Surah Info */}
        {currentSurah && (
          <div className="mb-4 p-3 bg-muted rounded-lg">
            <div className="flex items-center justify-between">
              <div>
                <h3 className="font-semibold">{currentSurah.name} - {currentSurah.englishName}</h3>
                <p className="text-sm text-muted-foreground">
                  {currentSurah.meaning} • {currentSurah.type} • {currentSurah.verses.length} {language === "ar" ? "آية" : "verses"}
                </p>
              </div>
              <Button variant="outline" size="sm" onClick={() => setShowFullSurah(true)}>
                {language === "ar" ? "السورة كاملة" : "Full Surah"}
              </Button>
            </div>
          </div>
        )}

        {/* Current Verse */}
        {currentVerse && (
          <>
            {/* Arabic Text */}
            <div className="mb-4 p-4 bg-muted rounded-xl rtl">
              <p className="text-2xl font-amiri leading-loose text-right text-foreground">
                {currentVerse.arabic}
              </p>
            </div>

            {/* Transliteration */}
            <div className="mb-4 p-3 bg-blue-50 dark:bg-blue-900/30 rounded-lg">
              <p className="text-sm font-medium text-blue-800 dark:text-blue-200 italic">
                {currentVerse.transliteration}
              </p>
            </div>

            {/* Translation */}
            <div className="mb-6 p-3 bg-green-50 dark:bg-green-900/30 rounded-lg">
              <p className="text-sm text-green-800 dark:text-green-200 leading-relaxed">
                {currentVerse.translation}
              </p>
            </div>
          </>
        )}

        {/* Audio Controls */}
        <div className="flex items-center justify-between bg-gray-100 dark:bg-gray-700 rounded-xl p-4 mb-4">
          <Button variant="ghost" size="sm" onClick={handlePrevious}>
            <SkipBack className="h-5 w-5" />
          </Button>
          <Button
            size="sm"
            onClick={togglePlayback}
            className="bg-emerald-600 hover:bg-emerald-700 text-white w-12 h-12 rounded-full"
          >
            {isPlaying ? <Pause className="h-5 w-5" /> : <Play className="h-5 w-5" />}
          </Button>
          <Button variant="ghost" size="sm" onClick={handleNext}>
            <SkipForward className="h-5 w-5" />
          </Button>
          <Select value={selectedReciter} onValueChange={setSelectedReciter}>
            <SelectTrigger className="w-40">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              {reciters.map((reciter) => (
                <SelectItem key={reciter.id} value={reciter.id}>
                  {language === "ar" ? reciter.arabicName : reciter.name}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>

        {/* Verse Navigation */}
        <div className="flex justify-between items-center">
          <span className="text-sm text-gray-600 dark:text-gray-400">
            {language === "ar" ? `آية ${currentAyah} من ${currentSurah?.verses.length || 0}` : `Verse ${currentAyah} of ${currentSurah?.verses.length || 0}`}
          </span>
          <div className="flex space-x-2">
            <Button variant="outline" size="sm" onClick={handlePrevious}>
              {language === "ar" ? "السابق" : "Previous"}
            </Button>
            <Button size="sm" onClick={handleNext} className="bg-emerald-600 hover:bg-emerald-700 text-white">
              {language === "ar" ? "التالي" : "Next"}
            </Button>
          </div>
        </div>
      </Card>
    </section>
  );
}
