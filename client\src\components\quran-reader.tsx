import { useState } from "react";
import { Card } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Badge } from "@/components/ui/badge";
import { ScrollArea } from "@/components/ui/scroll-area";
import { quranData, reciters } from "@/data/quran-data";
import { Play, Pause, SkipBack, SkipForward, Settings, ArrowLeft, BookOpen, Copy, Share2 } from "lucide-react";
import { useToast } from "@/hooks/use-toast";

interface QuranReaderProps {
  onBack?: () => void;
  language?: "en" | "ar";
}

export function QuranReader({ onBack, language = "en" }: QuranReaderProps) {
  const [selectedSurah, setSelectedSurah] = useState<number>(1);
  const [currentAyah, setCurrentAyah] = useState<number>(1);
  const [selectedReciter, setSelectedReciter] = useState<string>("mishary");
  const [isPlaying, setIsPlaying] = useState<boolean>(false);
  const [showFullSurah, setShowFullSurah] = useState<boolean>(false);
  const { toast } = useToast();

  const currentSurah = quranData.find(s => s.number === selectedSurah);
  const currentVerse = currentSurah?.verses?.[currentAyah - 1];

  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text);
    toast({
      title: language === "ar" ? "تم النسخ" : "Copied",
      description: language === "ar" ? "تم نسخ النص" : "Text copied to clipboard"
    });
  };

  const handlePrevious = () => {
    if (currentAyah > 1) {
      setCurrentAyah(currentAyah - 1);
    } else if (selectedSurah > 1) {
      const prevSurah = quranData.find(s => s.number === selectedSurah - 1);
      if (prevSurah) {
        setSelectedSurah(selectedSurah - 1);
        setCurrentAyah(prevSurah.verses.length);
      }
    }
  };

  const handleNext = () => {
    if (currentSurah && currentAyah < currentSurah.verses.length) {
      setCurrentAyah(currentAyah + 1);
    } else if (selectedSurah < quranData.length) {
      setSelectedSurah(selectedSurah + 1);
      setCurrentAyah(1);
    }
  };

  const togglePlayback = () => {
    setIsPlaying(!isPlaying);
  };

  if (showFullSurah) {
    return (
      <div className="min-h-screen bg-background">
        {/* Header */}
        <header className="bg-primary text-primary-foreground p-4 flex items-center">
          <Button variant="ghost" size="sm" onClick={() => setShowFullSurah(false)} className="mr-3 text-primary-foreground hover:bg-primary-foreground/20">
            <ArrowLeft className="h-5 w-5" />
          </Button>
          <div className="flex items-center space-x-3">
            <BookOpen className="h-6 w-6" />
            <div>
              <h1 className="text-xl font-bold">
                {currentSurah?.name} - {currentSurah?.englishName}
              </h1>
              <p className="text-sm opacity-90">
                {currentSurah?.meaning} • {currentSurah?.type} • {currentSurah?.verses.length} {language === "ar" ? "آية" : "verses"}
              </p>
            </div>
          </div>
        </header>

        <ScrollArea className="h-[calc(100vh-80px)] p-4">
          <div className="space-y-6">
            {currentSurah?.verses.map((verse, index) => (
              <Card key={verse.number} className="p-6">
                <div className="flex items-center justify-between mb-4">
                  <Badge className="bg-primary text-primary-foreground">
                    {language === "ar" ? `آية ${verse.number}` : `Verse ${verse.number}`}
                  </Badge>
                  <div className="flex items-center space-x-2">
                    <Button variant="ghost" size="sm" onClick={() => copyToClipboard(verse.arabic)}>
                      <Copy className="h-4 w-4" />
                    </Button>
                    <Button variant="ghost" size="sm">
                      <Share2 className="h-4 w-4" />
                    </Button>
                  </div>
                </div>

                {/* Arabic Text */}
                <div className="mb-4 p-4 bg-muted rounded-xl rtl">
                  <p className="text-xl font-amiri leading-loose text-right text-foreground">
                    {verse.arabic}
                  </p>
                </div>

                {/* Transliteration */}
                <div className="mb-4 p-3 bg-blue-50 dark:bg-blue-900/30 rounded-lg">
                  <p className="text-sm font-medium text-blue-800 dark:text-blue-200 italic">
                    {verse.transliteration}
                  </p>
                </div>

                {/* Translation */}
                <div className="p-3 bg-green-50 dark:bg-green-900/30 rounded-lg">
                  <p className="text-sm text-green-800 dark:text-green-200 leading-relaxed">
                    {verse.translation}
                  </p>
                </div>
              </Card>
            ))}
          </div>
        </ScrollArea>
      </div>
    );
  }

  return (
    <section className="p-3 sm:p-4 w-full">
      <Card className="p-4 sm:p-6 mx-auto max-w-lg">
        <div className="flex justify-between items-center mb-4 sm:mb-6">
          {onBack && (
            <Button variant="ghost" size="sm" onClick={onBack} className="mr-2 flex-shrink-0">
              <ArrowLeft className="h-4 w-4" />
            </Button>
          )}
          <h2 className="text-lg sm:text-xl font-semibold text-gray-800 dark:text-white min-w-0 flex-1 text-center">
            {language === "ar" ? "قارئ القرآن الكريم" : "Quran Reader"}
          </h2>
          <Button variant="ghost" size="sm" className="flex-shrink-0">
            <Settings className="h-4 w-4" />
          </Button>
        </div>

        {/* Surah Selection */}
        <div className="mb-4">
          <Select
            value={selectedSurah.toString()}
            onValueChange={(value) => {
              setSelectedSurah(parseInt(value));
              setCurrentAyah(1);
            }}
          >
            <SelectTrigger>
              <SelectValue placeholder={language === "ar" ? "اختر السورة" : "Select Surah"} />
            </SelectTrigger>
            <SelectContent>
              {quranData.map((surah) => (
                <SelectItem key={surah.number} value={surah.number.toString()}>
                  {surah.number}. {surah.name} ({surah.englishName})
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>

        {/* Surah Info */}
        {currentSurah && (
          <div className="mb-4 p-3 bg-muted rounded-lg">
            <div className="flex items-center justify-between">
              <div>
                <h3 className="font-semibold">{currentSurah.name} - {currentSurah.englishName}</h3>
                <p className="text-sm text-muted-foreground">
                  {currentSurah.meaning} • {currentSurah.type} • {currentSurah.verses.length} {language === "ar" ? "آية" : "verses"}
                </p>
              </div>
              <Button variant="outline" size="sm" onClick={() => setShowFullSurah(true)}>
                {language === "ar" ? "السورة كاملة" : "Full Surah"}
              </Button>
            </div>
          </div>
        )}

        {/* Current Verse */}
        {currentVerse && (
          <>
            {/* Arabic Text */}
            <div className="mb-4 p-4 bg-muted rounded-xl rtl">
              <p className="text-2xl font-amiri leading-loose text-right text-foreground">
                {currentVerse.arabic}
              </p>
            </div>

            {/* Transliteration */}
            <div className="mb-4 p-3 bg-blue-50 dark:bg-blue-900/30 rounded-lg">
              <p className="text-sm font-medium text-blue-800 dark:text-blue-200 italic">
                {currentVerse.transliteration}
              </p>
            </div>

            {/* Translation */}
            <div className="mb-6 p-3 bg-green-50 dark:bg-green-900/30 rounded-lg">
              <p className="text-sm text-green-800 dark:text-green-200 leading-relaxed">
                {currentVerse.translation}
              </p>
            </div>
          </>
        )}

        {/* Audio Controls */}
        <div className="flex items-center justify-between bg-gray-100 dark:bg-gray-700 rounded-xl p-4 mb-4">
          <Button variant="ghost" size="sm" onClick={handlePrevious}>
            <SkipBack className="h-5 w-5" />
          </Button>
          <Button
            size="sm"
            onClick={togglePlayback}
            className="bg-emerald-600 hover:bg-emerald-700 text-white w-12 h-12 rounded-full"
          >
            {isPlaying ? <Pause className="h-5 w-5" /> : <Play className="h-5 w-5" />}
          </Button>
          <Button variant="ghost" size="sm" onClick={handleNext}>
            <SkipForward className="h-5 w-5" />
          </Button>
          <Select value={selectedReciter} onValueChange={setSelectedReciter}>
            <SelectTrigger className="w-40">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              {reciters.map((reciter) => (
                <SelectItem key={reciter.id} value={reciter.id}>
                  {language === "ar" ? reciter.arabicName : reciter.name}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>

        {/* Verse Navigation */}
        <div className="flex justify-between items-center">
          <span className="text-sm text-gray-600 dark:text-gray-400">
            {language === "ar" ? `آية ${currentAyah} من ${currentSurah?.verses.length || 0}` : `Verse ${currentAyah} of ${currentSurah?.verses.length || 0}`}
          </span>
          <div className="flex space-x-2">
            <Button variant="outline" size="sm" onClick={handlePrevious}>
              {language === "ar" ? "السابق" : "Previous"}
            </Button>
            <Button size="sm" onClick={handleNext} className="bg-emerald-600 hover:bg-emerald-700 text-white">
              {language === "ar" ? "التالي" : "Next"}
            </Button>
          </div>
        </div>
      </Card>
    </section>
  );
}
