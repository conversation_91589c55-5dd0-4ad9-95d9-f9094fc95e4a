import { useState, useEffect } from 'react';
import { <PERSON>, <PERSON>Off, <PERSON>, Moon, <PERSON>, Minus, Plus } from 'lucide-react';
import { Button } from '../ui/button';
import { Card } from '../ui/card';
import { Slider } from '../ui/slider';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '../ui/select';

interface ReadingSettings {
  fontSize: number;
  lineHeight: number;
  fontFamily: string;
  contrast: 'normal' | 'high';
  nightMode: boolean;
  autoScroll: boolean;
  highlightMode: boolean;
}

export function ReadingModePanel({ isOpen, onClose }: { isOpen: boolean; onClose: () => void }) {
  const [settings, setSettings] = useState<ReadingSettings>({
    fontSize: 16,
    lineHeight: 1.6,
    fontFamily: 'Amiri',
    contrast: 'normal',
    nightMode: false,
    autoScroll: false,
    highlightMode: false
  });

  useEffect(() => {
    // Apply settings to root element
    const root = document.documentElement;
    root.style.setProperty('--reading-font-size', `${settings.fontSize}px`);
    root.style.setProperty('--reading-line-height', settings.lineHeight.toString());
    root.style.setProperty('--reading-font-family', settings.fontFamily);
    
    if (settings.contrast === 'high') {
      root.classList.add('high-contrast');
    } else {
      root.classList.remove('high-contrast');
    }
    
    if (settings.nightMode) {
      root.classList.add('night-mode');
    } else {
      root.classList.remove('night-mode');
    }
  }, [settings]);

  if (!isOpen) return null;

  return (
    <Card className="fixed top-4 right-4 w-80 p-4 shadow-lg z-50 max-h-96 overflow-y-auto">
      <div className="flex justify-between items-center mb-4">
        <h3 className="font-semibold text-lg">إعدادات القراءة</h3>
        <Button variant="ghost" size="sm" onClick={onClose}>
          <EyeOff className="h-4 w-4" />
        </Button>
      </div>

      <div className="space-y-4">
        {/* Font Size */}
        <div>
          <label className="text-sm font-medium flex items-center gap-2 mb-2">
            <Type className="h-4 w-4" />
            حجم الخط: {settings.fontSize}px
          </label>
          <div className="flex items-center gap-2">
            <Button
              size="sm"
              variant="outline"
              onClick={() => setSettings(prev => ({ ...prev, fontSize: Math.max(12, prev.fontSize - 2) }))}
            >
              <Minus className="h-3 w-3" />
            </Button>
            <Slider
              value={[settings.fontSize]}
              onValueChange={([value]) => setSettings(prev => ({ ...prev, fontSize: value }))}
              min={12}
              max={28}
              step={2}
              className="flex-1"
            />
            <Button
              size="sm"
              variant="outline"
              onClick={() => setSettings(prev => ({ ...prev, fontSize: Math.min(28, prev.fontSize + 2) }))}
            >
              <Plus className="h-3 w-3" />
            </Button>
          </div>
        </div>

        {/* Line Height */}
        <div>
          <label className="text-sm font-medium mb-2 block">
            تباعد الأسطر: {settings.lineHeight}
          </label>
          <Slider
            value={[settings.lineHeight]}
            onValueChange={([value]) => setSettings(prev => ({ ...prev, lineHeight: value }))}
            min={1.2}
            max={2.5}
            step={0.1}
          />
        </div>

        {/* Font Family */}
        <div>
          <label className="text-sm font-medium mb-2 block">نوع الخط</label>
          <Select
            value={settings.fontFamily}
            onValueChange={(fontFamily) => setSettings(prev => ({ ...prev, fontFamily }))}
          >
            <SelectTrigger>
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="Amiri">أميري (Amiri)</SelectItem>
              <SelectItem value="Noto Sans Arabic">نوتو سانس عربي</SelectItem>
              <SelectItem value="Cairo">القاهرة (Cairo)</SelectItem>
              <SelectItem value="Tajawal">تجوال (Tajawal)</SelectItem>
            </SelectContent>
          </Select>
        </div>

        {/* Contrast */}
        <div className="flex items-center justify-between">
          <span className="text-sm font-medium">تباين عالي</span>
          <Button
            variant={settings.contrast === 'high' ? "default" : "outline"}
            size="sm"
            onClick={() => setSettings(prev => ({ 
              ...prev, 
              contrast: prev.contrast === 'high' ? 'normal' : 'high' 
            }))}
          >
            <Eye className="h-4 w-4" />
          </Button>
        </div>

        {/* Night Mode */}
        <div className="flex items-center justify-between">
          <span className="text-sm font-medium">الوضع الليلي</span>
          <Button
            variant={settings.nightMode ? "default" : "outline"}
            size="sm"
            onClick={() => setSettings(prev => ({ ...prev, nightMode: !prev.nightMode }))}
          >
            {settings.nightMode ? <Moon className="h-4 w-4" /> : <Sun className="h-4 w-4" />}
          </Button>
        </div>

        {/* Reset Button */}
        <Button
          variant="outline"
          className="w-full"
          onClick={() => setSettings({
            fontSize: 16,
            lineHeight: 1.6,
            fontFamily: 'Amiri',
            contrast: 'normal',
            nightMode: false,
            autoScroll: false,
            highlightMode: false
          })}
        >
          إعادة تعيين الإعدادات
        </Button>
      </div>
    </Card>
  );
}

export function ReadingModeButton() {
  const [isOpen, setIsOpen] = useState(false);

  return (
    <>
      <Button
        variant="ghost"
        size="sm"
        onClick={() => setIsOpen(true)}
        className="fixed top-4 left-4 z-40 bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm shadow-md"
      >
        <Eye className="h-4 w-4" />
        <span className="sr-only">إعدادات القراءة</span>
      </Button>
      
      <ReadingModePanel isOpen={isOpen} onClose={() => setIsOpen(false)} />
    </>
  );
}