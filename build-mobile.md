# دليل بناء التطبيق الإسلامي الشامل للهواتف المحمولة

## متطلبات التطبيق

### للأندرويد:
- Android Studio 4.0 أو أحدث
- Android SDK 29 أو أحدث
- Java JDK 11 أو أحدث
- Gradle 7.0 أو أحدث

### لنظام iOS:
- Xcode 12 أو أحدث
- iOS 13.0 أو أحدث
- macOS 10.15 أو أحدث
- CocoaPods

## خطوات التثبيت والبناء

### 1. تثبيت Capacitor

```bash
# تثبيت Capacitor CLI
npm install -g @capacitor/cli

# تثبيت المكونات الأساسية
npm install @capacitor/core @capacitor/android @capacitor/ios
```

### 2. تثبيت المكونات الإضافية المطلوبة

```bash
# المكونات الأساسية
npm install @capacitor/geolocation @capacitor/device @capacitor/haptics
npm install @capacitor/local-notifications @capacitor/status-bar
npm install @capacitor/keyboard @capacitor/storage @capacitor/network
npm install @capacitor/share @capacitor/toast @capacitor/screen-orientation

# للإشعارات المتقدمة
npm install @capacitor/push-notifications

# للصوتيات
npm install @capacitor/filesystem @capacitor/http
```

### 3. بناء التطبيق

```bash
# بناء الواجهة الأمامية
npm run build

# إضافة منصات الهواتف
npx cap add android
npx cap add ios

# مزامنة الملفات
npx cap sync
```

### 4. تشغيل التطبيق

#### للأندرويد:
```bash
# فتح في Android Studio
npx cap open android

# أو التشغيل المباشر
npx cap run android
```

#### لنظام iOS:
```bash
# فتح في Xcode
npx cap open ios

# أو التشغيل المباشر
npx cap run ios
```

## الميزات المحمولة المدعومة

### ✅ الميزات المفعلة:
- 📍 **تحديد الموقع**: لحساب أوقات الصلاة واتجاه القبلة
- 📳 **الاهتزاز**: للتسبيح والإشعارات
- 🔔 **الإشعارات المحلية**: لتنبيهات أوقات الصلاة
- 💾 **التخزين المحلي**: لحفظ الإعدادات والإحصائيات
- 🌐 **حالة الشبكة**: للعمل بدون إنترنت
- 📤 **المشاركة**: لمشاركة الآيات والأذكار
- 🔧 **شريط الحالة**: تخصيص مظهر التطبيق
- ⌨️ **لوحة المفاتيح**: تحسين إدخال النصوص
- 🔄 **اتجاه الشاشة**: حماية الاتجاه الطولي
- 🏪 **PWA**: إمكانية التثبيت كتطبيق ويب

### 🎯 الأذونات المطلوبة:

#### أندرويد:
- `ACCESS_FINE_LOCATION`: لتحديد الموقع الدقيق
- `ACCESS_COARSE_LOCATION`: لتحديد الموقع التقريبي
- `VIBRATE`: للاهتزاز في التسبيح
- `POST_NOTIFICATIONS`: لإشعارات أوقات الصلاة
- `INTERNET`: للاتصال بالإنترنت
- `ACCESS_NETWORK_STATE`: لمراقبة حالة الشبكة

#### iOS:
- `NSLocationWhenInUseUsageDescription`: للموقع أثناء الاستخدام
- `NSMotionUsageDescription`: لمستشعر الحركة (البوصلة)

## إعدادات التطبيق

### معرف التطبيق:
- **Package ID**: `com.islamicapp.complete`
- **App Name**: `التطبيق الإسلامي الشامل`

### الإعدادات المخصصة:
- **اللون الأساسي**: `#10b981` (أخضر إسلامي)
- **دعم RTL**: مفعل للنصوص العربية
- **الاتجاه**: طولي فقط لتجربة مثلى
- **الإشعارات**: مخصصة لأوقات الصلاة

## خطوات النشر

### للأندرويد (Google Play Store):

1. **إنشاء مفتاح التوقيع**:
```bash
keytool -genkey -v -keystore islamic-app-release.keystore -alias islamic-app -keyalg RSA -keysize 2048 -validity 10000
```

2. **بناء APK للنشر**:
```bash
cd android
./gradlew assembleRelease
```

3. **بناء AAB للمتجر**:
```bash
./gradlew bundleRelease
```

### لنظام iOS (App Store):

1. **إعداد الشهادات في Xcode**
2. **تحديد معرف التطبيق في Apple Developer**
3. **بناء الأرشيف**:
   - Product → Archive في Xcode
   - Upload to App Store Connect

## اختبار التطبيق

### اختبار على الأجهزة الفعلية:
```bash
# أندرويد - عبر USB debugging
npx cap run android --target device

# iOS - عبر Xcode
npx cap run ios --target device
```

### اختبار الميزات الأساسية:
- ✅ أوقات الصلاة تعمل بالموقع الصحيح
- ✅ اتجاه القبلة يشير للاتجاه الصحيح
- ✅ الإشعارات تعمل في الخلفية
- ✅ التسبيح يهتز عند العد
- ✅ التطبيق يعمل بدون إنترنت
- ✅ المشاركة تعمل مع التطبيقات الأخرى

## استكشاف الأخطاء

### مشاكل شائعة وحلولها:

1. **خطأ في الأذونات**:
   - تأكد من إضافة جميع الأذونات في `AndroidManifest.xml`
   - للـ iOS، تأكد من إضافة descriptions في `Info.plist`

2. **مشاكل في تحديد الموقع**:
   - تأكد من تفعيل GPS على الجهاز
   - امنح الأذونات من إعدادات التطبيق

3. **مشاكل في البناء**:
   - قم بتنظيف المشروع: `npx cap clean`
   - أعد المزامنة: `npx cap sync`

4. **مشاكل في الإشعارات**:
   - تأكد من تفعيل الإشعارات في إعدادات الجهاز
   - للأندرويد 13+، تأكد من أذونات الإشعارات

## الأمان والخصوصية

- 🔒 جميع البيانات محفوظة محلياً على الجهاز
- 🚫 لا يتم جمع أي بيانات شخصية
- 📍 الموقع يُستخدم فقط لحساب أوقات الصلاة
- 🔐 لا توجد خوادم خارجية للبيانات الحساسة

## الدعم التقني

للحصول على المساعدة أو الإبلاغ عن مشاكل:
- تأكد من تحديث جميع المكتبات
- تحقق من إعدادات الجهاز والأذونات
- راجع سجلات النظام للأخطاء التفصيلية

---

**ملاحظة**: هذا التطبيق مصمم ليعمل كتطبيق هجين (Hybrid) مع إمكانية الوصول لجميع ميزات النظام المطلوبة للتطبيق الإسلامي الشامل.