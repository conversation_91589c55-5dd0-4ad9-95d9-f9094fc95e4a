import { pgTable, text, serial, integer, boolean, timestamp, real } from "drizzle-orm/pg-core";
import { createInsertSchema } from "drizzle-zod";
import { z } from "zod";

export const users = pgTable("users", {
  id: serial("id").primaryKey(),
  username: text("username").notNull().unique(),
  password: text("password").notNull(),
});

export const userPreferences = pgTable("user_preferences", {
  id: serial("id").primaryKey(),
  userId: integer("user_id").references(() => users.id),
  darkMode: boolean("dark_mode").default(false),
  language: text("language").default("en"),
  latitude: real("latitude"),
  longitude: real("longitude"),
  city: text("city"),
  country: text("country"),
  calculationMethod: text("calculation_method").default("MuslimWorldLeague"),
  madhab: text("madhab").default("Shafi"),
});

export const prayerTracking = pgTable("prayer_tracking", {
  id: serial("id").primaryKey(),
  userId: integer("user_id").references(() => users.id),
  prayer: text("prayer").notNull(), // fajr, dhuhr, asr, maghrib, isha
  date: timestamp("date").notNull(),
  completed: boolean("completed").default(false),
});

export const dhikrCounts = pgTable("dhikr_counts", {
  id: serial("id").primaryKey(),
  userId: integer("user_id").references(() => users.id),
  dhikrType: text("dhikr_type").notNull(), // subhanallah, alhamdulillah, etc.
  count: integer("count").default(0),
  date: timestamp("date").notNull(),
});

export const quranProgress = pgTable("quran_progress", {
  id: serial("id").primaryKey(),
  userId: integer("user_id").references(() => users.id),
  surah: integer("surah").notNull(),
  ayah: integer("ayah").notNull(),
  pagesRead: integer("pages_read").default(0),
  timeSpent: integer("time_spent").default(0), // in minutes
  lastRead: timestamp("last_read").notNull(),
});

export const insertUserSchema = createInsertSchema(users).pick({
  username: true,
  password: true,
});

export const insertUserPreferencesSchema = createInsertSchema(userPreferences).omit({
  id: true,
});

export const insertPrayerTrackingSchema = createInsertSchema(prayerTracking).omit({
  id: true,
});

export const insertDhikrCountSchema = createInsertSchema(dhikrCounts).omit({
  id: true,
});

export const insertQuranProgressSchema = createInsertSchema(quranProgress).omit({
  id: true,
});

export type InsertUser = z.infer<typeof insertUserSchema>;
export type User = typeof users.$inferSelect;
export type UserPreferences = typeof userPreferences.$inferSelect;
export type InsertUserPreferences = z.infer<typeof insertUserPreferencesSchema>;
export type PrayerTracking = typeof prayerTracking.$inferSelect;
export type InsertPrayerTracking = z.infer<typeof insertPrayerTrackingSchema>;
export type DhikrCount = typeof dhikrCounts.$inferSelect;
export type InsertDhikrCount = z.infer<typeof insertDhikrCountSchema>;
export type QuranProgress = typeof quranProgress.$inferSelect;
export type InsertQuranProgress = z.infer<typeof insertQuranProgressSchema>;
