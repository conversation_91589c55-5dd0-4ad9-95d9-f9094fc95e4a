<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no, viewport-fit=cover" />
    <meta name="theme-color" content="#10b981" />
    <meta name="apple-mobile-web-app-capable" content="yes" />
    <meta name="apple-mobile-web-app-status-bar-style" content="default" />
    <meta name="apple-mobile-web-app-title" content="Islamic App" />
    <meta name="mobile-web-app-capable" content="yes" />
    <meta name="format-detection" content="telephone=no" />
    <link rel="manifest" href="/manifest.json" />
    <link rel="icon" type="image/png" sizes="192x192" href="/icon-192x192.png" />
    <link rel="apple-touch-icon" href="/icon-192x192.png" />
    <title>التطبيق الإسلامي الشامل - Islamic App</title>
    <meta name="description" content="تطبيق إسلامي شامل يحتوي على أوقات الصلاة والقرآن الكريم والأذكار والأحاديث النبوية الشريفة" />
    <meta name="keywords" content="إسلامي، صلاة، قرآن، أذكار، أحاديث، قبلة، تسبيح، Islamic, Prayer, Quran, Dhikr, Hadith" />
    
    <!-- Open Graph Tags -->
    <meta property="og:title" content="التطبيق الإسلامي الشامل" />
    <meta property="og:description" content="تطبيق إسلامي شامل يحتوي على أوقات الصلاة والقرآن الكريم والأذكار والأحاديث النبوية الشريفة" />
    <meta property="og:type" content="website" />
    <meta property="og:image" content="/icon-512x512.png" />
    <meta property="og:locale" content="ar_SA" />
    
    <!-- Twitter Card -->
    <meta name="twitter:card" content="summary_large_image" />
    <meta name="twitter:title" content="التطبيق الإسلامي الشامل" />
    <meta name="twitter:description" content="تطبيق إسلامي شامل يحتوي على أوقات الصلاة والقرآن الكريم والأذكار والأحاديث النبوية الشريفة" />
    <meta name="twitter:image" content="/icon-512x512.png" />
    <style>
      /* Prevent zoom and improve mobile UX */
      * {
        touch-action: manipulation;
        -webkit-tap-highlight-color: transparent;
      }
      
      html, body {
        height: 100%;
        overflow-x: hidden;
        -webkit-text-size-adjust: 100%;
        -ms-text-size-adjust: 100%;
      }
      
      /* Loading screen */
      #loading {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100vh;
        height: 100dvh;
        background: linear-gradient(135deg, #10b981, #3b82f6);
        display: flex;
        align-items: center;
        justify-content: center;
        z-index: 9999;
        transition: opacity 0.3s ease;
      }
      
      #loading.fade-out {
        opacity: 0;
        pointer-events: none;
      }
      
      .loading-content {
        text-align: center;
        color: white;
      }
      
      .loading-spinner {
        width: 40px;
        height: 40px;
        border: 3px solid rgba(255,255,255,0.3);
        border-top: 3px solid white;
        border-radius: 50%;
        animation: spin 1s linear infinite;
        margin: 0 auto 16px;
      }
      
      .loading-text {
        font-size: 18px;
        font-weight: 600;
        margin-bottom: 8px;
        font-family: system-ui, -apple-system, sans-serif;
      }
      
      .loading-subtext {
        font-size: 14px;
        opacity: 0.8;
        font-family: system-ui, -apple-system, sans-serif;
      }
      
      @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
      }
    </style>
  </head>
  <body>
    <div id="loading">
      <div class="loading-content">
        <div class="loading-spinner"></div>
        <div class="loading-text">Islamic App</div>
        <div class="loading-subtext">Loading your spiritual companion...</div>
      </div>
    </div>
    <div id="root"></div>
    <script type="module" src="/src/main.tsx"></script>
    <script>
      // Hide loading screen when app loads
      window.addEventListener('load', () => {
        setTimeout(() => {
          const loading = document.getElementById('loading');
          if (loading) {
            loading.classList.add('fade-out');
            setTimeout(() => loading.remove(), 300);
          }
        }, 800);
      });
      
      // Prevent zoom on iOS
      document.addEventListener('gesturestart', function (e) {
        e.preventDefault();
      });
      
      // Prevent double-tap zoom
      let lastTouchEnd = 0;
      document.addEventListener('touchend', function (event) {
        const now = (new Date()).getTime();
        if (now - lastTouchEnd <= 300) {
          event.preventDefault();
        }
        lastTouchEnd = now;
      }, false);
      
      // Prevent pull-to-refresh on mobile
      document.body.addEventListener('touchstart', e => {
        if (e.touches.length === 1 && e.touches[0].clientY <= 50) {
          e.preventDefault();
        }
      }, { passive: false });
      
      document.body.addEventListener('touchmove', e => {
        if (e.touches.length === 1 && window.scrollY === 0) {
          e.preventDefault();
        }
      }, { passive: false });
    </script>
    <!-- This is a replit script which adds a banner on the top of the page when opened in development mode outside the replit environment -->
    <script type="text/javascript" src="https://replit.com/public/js/replit-dev-banner.js"></script>
  </body>
</html>