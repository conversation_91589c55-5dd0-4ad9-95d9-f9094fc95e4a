<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>CFBundleDevelopmentRegion</key>
	<string>ar</string>
	<key>CFBundleDisplayName</key>
	<string>التطبيق الإسلامي الشامل</string>
	<key>CFBundleExecutable</key>
	<string>$(EXECUTABLE_NAME)</string>
	<key>CFBundleIdentifier</key>
	<string>$(PRODUCT_BUNDLE_IDENTIFIER)</string>
	<key>CFBundleInfoDictionaryVersion</key>
	<string>6.0</string>
	<key>CFBundleName</key>
	<string>$(PRODUCT_NAME)</string>
	<key>CFBundlePackageType</key>
	<string>APPL</string>
	<key>CFBundleShortVersionString</key>
	<string>$(MARKETING_VERSION)</string>
	<key>CFBundleVersion</key>
	<string>$(CURRENT_PROJECT_VERSION)</string>
	<key>LSRequiresIPhoneOS</key>
	<true/>
	<key>UIMainStoryboardFile</key>
	<string>Main</string>
	<key>UIRequiredDeviceCapabilities</key>
	<array>
		<string>armv7</string>
	</array>
	<key>UISupportedInterfaceOrientations</key>
	<array>
		<string>UIInterfaceOrientationPortrait</string>
		<string>UIInterfaceOrientationPortraitUpsideDown</string>
	</array>
	<key>UISupportedInterfaceOrientations~ipad</key>
	<array>
		<string>UIInterfaceOrientationPortrait</string>
		<string>UIInterfaceOrientationPortraitUpsideDown</string>
		<string>UIInterfaceOrientationLandscapeLeft</string>
		<string>UIInterfaceOrientationLandscapeRight</string>
	</array>
	<key>UIViewControllerBasedStatusBarAppearance</key>
	<true/>
	
	<!-- Privacy Descriptions -->
	<key>NSLocationWhenInUseUsageDescription</key>
	<string>يحتاج التطبيق للموقع لتحديد أوقات الصلاة واتجاه القبلة بدقة</string>
	<key>NSLocationAlwaysAndWhenInUseUsageDescription</key>
	<string>يحتاج التطبيق للموقع لتحديد أوقات الصلاة واتجاه القبلة بدقة</string>
	<key>NSCameraUsageDescription</key>
	<string>يمكن استخدام الكاميرا لمشاركة الآيات والأذكار</string>
	<key>NSMicrophoneUsageDescription</key>
	<string>قد يحتاج التطبيق للميكروفون للميزات الصوتية</string>
	<key>NSMotionUsageDescription</key>
	<string>يستخدم التطبيق مستشعر الحركة لتحديد اتجاه القبلة</string>
	
	<!-- App Transport Security -->
	<key>NSAppTransportSecurity</key>
	<dict>
		<key>NSAllowsArbitraryLoads</key>
		<true/>
	</dict>
	
	<!-- Background Modes -->
	<key>UIBackgroundModes</key>
	<array>
		<string>background-fetch</string>
		<string>background-processing</string>
	</array>
	
	<!-- Arabic Language Support -->
	<key>CFBundleLocalizations</key>
	<array>
		<string>ar</string>
		<string>en</string>
	</array>
	
	<!-- Right-to-Left Support -->
	<key>CFBundleDevelopmentRegion</key>
	<string>ar</string>
	
	<!-- Status Bar Style -->
	<key>UIStatusBarStyle</key>
	<string>UIStatusBarStyleLightContent</string>
	
	<!-- Launch Screen -->
	<key>UILaunchStoryboardName</key>
	<string>LaunchScreen</string>
</dict>
</plist>