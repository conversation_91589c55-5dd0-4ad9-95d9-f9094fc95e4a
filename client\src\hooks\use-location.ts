import { useState, useEffect } from "react";

interface LocationData {
  latitude: number;
  longitude: number;
  city?: string;
  country?: string;
}

export function useLocation() {
  const [location, setLocation] = useState<LocationData | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    if ("geolocation" in navigator) {
      navigator.geolocation.getCurrentPosition(
        async (position) => {
          const { latitude, longitude } = position.coords;
          
          try {
            // Try to get city/country information using reverse geocoding
            // In production, use a proper geocoding service
            setLocation({
              latitude,
              longitude,
              city: "Current Location",
              country: "Unknown",
            });
          } catch (err) {
            setLocation({ latitude, longitude });
          }
          
          setLoading(false);
        },
        (err) => {
          setError(err.message);
          setLoading(false);
          
          // Fallback to a default location (Mecca)
          setLocation({
            latitude: 21.4225,
            longitude: 39.8262,
            city: "Mecca",
            country: "Saudi Arabia",
          });
        },
        {
          enableHighAccuracy: true,
          timeout: 10000,
          maximumAge: 300000, // 5 minutes
        }
      );
    } else {
      setError("Geolocation is not supported by this browser");
      setLoading(false);
      
      // Fallback to default location
      setLocation({
        latitude: 21.4225,
        longitude: 39.8262,
        city: "Mecca",
        country: "Saudi Arabia",
      });
    }
  }, []);

  return { location, loading, error };
}
