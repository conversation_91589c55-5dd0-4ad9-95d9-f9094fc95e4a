import { useState } from "react";
import { Button } from "@/components/ui/button";
import { Clock, Compass, Book, Hand, Calendar, BarChart, BookOpen, MessageSquare, Settings, Menu, X } from "lucide-react";
import { motion, AnimatePresence } from "framer-motion";

type Section = "prayer" | "qibla" | "quran" | "tasbih" | "calendar" | "stats" | "adhkar" | "hadith" | "settings";

interface FloatingMenuProps {
  activeSection: Section;
  onSectionChange: (section: Section) => void;
  language: "en" | "ar";
}

export function FloatingMenu({ activeSection, onSectionChange, language }: FloatingMenuProps) {
  const [isOpen, setIsOpen] = useState(false);

  const menuItems = [
    { id: "prayer", icon: Clock, label: language === "ar" ? "الصلاة" : "Prayer", color: "bg-blue-500" },
    { id: "qibla", icon: Compass, label: language === "ar" ? "القبلة" : "Qibla", color: "bg-green-500" },
    { id: "quran", icon: Book, label: language === "ar" ? "القرآن" : "Quran", color: "bg-emerald-500" },
    { id: "adhkar", icon: BookOpen, label: language === "ar" ? "الأذكار" : "Adhkar", color: "bg-purple-500" },
    { id: "hadith", icon: MessageSquare, label: language === "ar" ? "الأحاديث" : "Hadith", color: "bg-orange-500" },
    { id: "tasbih", icon: Hand, label: language === "ar" ? "التسبيح" : "Tasbih", color: "bg-yellow-500" },
    { id: "calendar", icon: Calendar, label: language === "ar" ? "التقويم" : "Calendar", color: "bg-red-500" },
    { id: "stats", icon: BarChart, label: language === "ar" ? "الإحصائيات" : "Statistics", color: "bg-indigo-500" },
    { id: "settings", icon: Settings, label: language === "ar" ? "الإعدادات" : "Settings", color: "bg-gray-500" },
  ] as const;

  const toggleMenu = () => {
    setIsOpen(!isOpen);
  };

  const handleItemClick = (sectionId: Section) => {
    onSectionChange(sectionId);
    setIsOpen(false);
  };

  return (
    <div className="fixed bottom-4 right-4 z-50 touch-manipulation">
      {/* Menu Items */}
      <AnimatePresence>
        {isOpen && (
          <motion.div
            initial={{ opacity: 0, scale: 0.8 }}
            animate={{ opacity: 1, scale: 1 }}
            exit={{ opacity: 0, scale: 0.8 }}
            transition={{ duration: 0.2 }}
            className="absolute bottom-20 right-0 space-y-2 max-h-[70vh] overflow-y-auto"
          >
            {menuItems.map((item, index) => (
              <motion.div
                key={item.id}
                initial={{ opacity: 0, x: 50, y: 10 }}
                animate={{ opacity: 1, x: 0, y: 0 }}
                exit={{ opacity: 0, x: 50, y: 10 }}
                transition={{ delay: index * 0.03, duration: 0.15 }}
                className="flex items-center justify-end"
              >
                <motion.div
                  initial={{ opacity: 0, x: 20 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ delay: index * 0.03 + 0.08 }}
                  className="mr-2 px-2 py-1 bg-white dark:bg-gray-800 rounded-lg shadow-md text-xs font-medium text-gray-800 dark:text-white border whitespace-nowrap"
                >
                  {item.label}
                </motion.div>
                <Button
                  onClick={() => handleItemClick(item.id)}
                  className={`w-11 h-11 rounded-full ${item.color} hover:opacity-90 text-white shadow-md active:scale-95 transition-all ${
                    activeSection === item.id ? "ring-2 ring-white ring-opacity-70 scale-110" : ""
                  }`}
                  size="sm"
                >
                  <item.icon className="h-4 w-4" />
                </Button>
              </motion.div>
            ))}
          </motion.div>
        )}
      </AnimatePresence>

      {/* Main Toggle Button */}
      <Button
        onClick={toggleMenu}
        className={`w-16 h-16 rounded-full shadow-lg transition-all duration-300 active:scale-95 ${
          isOpen 
            ? "bg-red-500 hover:bg-red-600 rotate-180" 
            : "bg-primary hover:bg-primary/90"
        } text-white touch-manipulation`}
        size="sm"
      >
        {isOpen ? (
          <X className="h-7 w-7" />
        ) : (
          <Menu className="h-7 w-7" />
        )}
      </Button>

      {/* Active Section Indicator */}
      {!isOpen && (
        <motion.div
          initial={{ opacity: 0, scale: 0 }}
          animate={{ opacity: 1, scale: 1 }}
          className="absolute -top-1 -left-1 w-5 h-5 bg-green-400 rounded-full flex items-center justify-center"
        >
          <div className="w-2 h-2 bg-white rounded-full animate-pulse"></div>
        </motion.div>
      )}
    </div>
  );
}