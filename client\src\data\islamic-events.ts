// Islamic Events and Important Dates from authentic Islamic calendar
// Source: Umm al-Qura calendar and verified Islamic institutions

export interface IslamicEvent {
  id: string;
  name: string;
  arabicName: string;
  hijriDate: string;
  description: string;
  type: 'religious' | 'historical' | 'seasonal';
  significance: string;
  recommendedActions?: string[];
}

export const islamicEvents: IslamicEvent[] = [
  {
    id: "muharram-1",
    name: "Islamic New Year",
    arabicName: "رأس السنة الهجرية",
    hijriDate: "1 محرم",
    description: "Beginning of the Islamic Hijri year, marking the migration of <PERSON> (PBUH) from Mecca to Medina",
    type: "historical",
    significance: "Commemorates the Hijra (migration) which marked the beginning of the Islamic calendar",
    recommendedActions: [
      "Reflect on the past year and make du'a for the new year",
      "Read about the significance of Hijra",
      "Make resolutions for spiritual improvement"
    ]
  },
  {
    id: "muharram-10",
    name: "Day of Ashura",
    arabicName: "يوم عاشوراء",
    hijriDate: "10 محرم",
    description: "A significant day in Islamic history when <PERSON> saved <PERSON> (<PERSON>) and the Israelites from Pharaoh",
    type: "religious",
    significance: "<PERSON> (PBUH) recommended fasting on this day for expiation of sins from the previous year",
    recommendedActions: [
      "Fast on the 10th of Muharram",
      "Fast on the 9th and 10th or 10th and 11th (recommended)",
      "Increase dhikr and remembrance of Allah",
      "Give charity to the poor"
    ]
  },
  {
    id: "rabiul-awwal-12",
    name: "Mawlid an-Nabi",
    arabicName: "المولد النبوي الشريف",
    hijriDate: "12 ربيع الأول",
    description: "Commemorating the birth of Prophet Muhammad (peace be upon him)",
    type: "religious",
    significance: "Celebrating the birth of the final messenger and reflecting on his teachings",
    recommendedActions: [
      "Read about the life of Prophet Muhammad (PBUH)",
      "Increase sending blessings upon the Prophet",
      "Study his hadith and sunnah",
      "Practice his teachings in daily life"
    ]
  },
  {
    id: "rajab-27",
    name: "Isra and Mi'raj",
    arabicName: "الإسراء والمعراج",
    hijriDate: "27 رجب",
    description: "The night journey of Prophet Muhammad (PBUH) from Mecca to Jerusalem and his ascension to the heavens",
    type: "religious",
    significance: "One of the greatest miracles of Prophet Muhammad (PBUH) and when the five daily prayers were prescribed",
    recommendedActions: [
      "Increase night prayers and dhikr",
      "Read about the details of Isra and Mi'raj",
      "Reflect on the importance of Salah",
      "Visit the mosque for special prayers if available"
    ]
  },
  {
    id: "shaban-15",
    name: "Laylat al-Bara'at",
    arabicName: "ليلة البراءة (ليلة النصف من شعبان)",
    hijriDate: "15 شعبان",
    description: "The night of the middle of Sha'ban, considered blessed in Islamic tradition",
    type: "religious",
    significance: "A night when Allah's mercy and forgiveness are abundant, and fates for the coming year are decided",
    recommendedActions: [
      "Spend the night in prayer and dhikr",
      "Seek Allah's forgiveness (istighfar)",
      "Fast the following day",
      "Visit graves of loved ones and make du'a for them"
    ]
  },
  {
    id: "ramadan-1",
    name: "Beginning of Ramadan",
    arabicName: "بداية شهر رمضان المبارك",
    hijriDate: "1 رمضان",
    description: "The start of the holy month of fasting, prayer, and spiritual reflection",
    type: "religious",
    significance: "One of the five pillars of Islam, a month of fasting, prayer, charity, and self-discipline",
    recommendedActions: [
      "Begin daily fasting from dawn to sunset",
      "Increase Quran recitation and study",
      "Perform Tarawih prayers",
      "Increase charity and helping the poor",
      "Seek Laylat al-Qadr in the last 10 nights"
    ]
  },
  {
    id: "ramadan-27",
    name: "Laylat al-Qadr",
    arabicName: "ليلة القدر",
    hijriDate: "27 رمضان (الليلة المرجحة)",
    description: "The Night of Power when the Quran was first revealed, better than a thousand months",
    type: "religious",
    significance: "The most blessed night of the year, when angels descend and prayers are especially answered",
    recommendedActions: [
      "Spend the entire night in worship",
      "Recite Quran extensively",
      "Make abundant du'a and istighfar",
      "Perform I'tikaf if possible",
      "Seek this night in the last 10 odd nights of Ramadan"
    ]
  },
  {
    id: "shawwal-1",
    name: "Eid al-Fitr",
    arabicName: "عيد الفطر المبارك",
    hijriDate: "1 شوال",
    description: "The festival of breaking the fast, celebrating the end of Ramadan",
    type: "religious",
    significance: "A day of joy, gratitude, and community celebration for completing the month of fasting",
    recommendedActions: [
      "Perform Eid prayer in congregation",
      "Give Zakat al-Fitr before Eid prayer",
      "Wear new or best clothes",
      "Exchange greetings and visit family",
      "Share food with neighbors and the poor",
      "Make takbir from sunset of last Ramadan day"
    ]
  },
  {
    id: "dhulhijjah-8-13",
    name: "Hajj Pilgrimage Days",
    arabicName: "أيام الحج المبارك",
    hijriDate: "8-13 ذو الحجة",
    description: "The days of Hajj pilgrimage to Mecca, one of the five pillars of Islam",
    type: "religious",
    significance: "Annual pilgrimage that every able Muslim should perform once in their lifetime",
    recommendedActions: [
      "For pilgrims: Follow all Hajj rituals",
      "For non-pilgrims: Fast and increase worship",
      "Make abundant dhikr and du'a",
      "Give charity and help others",
      "Study the significance of Hajj"
    ]
  },
  {
    id: "dhulhijjah-9",
    name: "Day of Arafat",
    arabicName: "يوم عرفة",
    hijriDate: "9 ذو الحجة",
    description: "The most important day of Hajj when pilgrims stand at Mount Arafat",
    type: "religious",
    significance: "The day when religion was perfected and Allah's favor was completed upon Muslims",
    recommendedActions: [
      "Fast this day (for non-pilgrims)",
      "Make extensive du'a throughout the day",
      "Increase istighfar and dhikr",
      "Give charity generously",
      "Read Quran and reflect on Allah's mercy"
    ]
  },
  {
    id: "dhulhijjah-10",
    name: "Eid al-Adha",
    arabicName: "عيد الأضحى المبارك",
    hijriDate: "10 ذو الحجة",
    description: "The festival of sacrifice, commemorating Prophet Ibrahim's willingness to sacrifice his son",
    type: "religious",
    significance: "Celebrates obedience to Allah and commemorates Prophet Ibrahim's ultimate act of faith",
    recommendedActions: [
      "Perform Eid prayer in congregation",
      "Sacrifice an animal (Qurbani) if able",
      "Distribute meat to family, friends, and poor",
      "Make takbir from Fajr of 9th to Asr of 13th",
      "Visit family and exchange greetings",
      "Give charity to those in need"
    ]
  },
  {
    id: "dhulhijjah-11-13",
    name: "Days of Tashreeq",
    arabicName: "أيام التشريق",
    hijriDate: "11-13 ذو الحجة",
    description: "The days following Eid al-Adha, known as the days of eating, drinking, and remembering Allah",
    type: "religious",
    significance: "Days of celebration and continued remembrance of Allah's blessings",
    recommendedActions: [
      "Continue making takbir until Asr of 13th",
      "Maintain the spirit of celebration",
      "Continue sharing food with others",
      "Reflect on the lessons of Hajj and sacrifice",
      "Strengthen family and community bonds"
    ]
  }
];

// Important weekly observances
export const weeklyObservances = [
  {
    day: "friday",
    name: "Jumu'ah (Friday)",
    arabicName: "يوم الجمعة",
    significance: "The blessed day of the week for Muslims",
    recommendations: [
      "Attend Jumu'ah prayer in congregation",
      "Increase sending blessings upon Prophet Muhammad (PBUH)",
      "Read Surah Al-Kahf",
      "Make du'a between Asr and Maghrib",
      "Perform ghusl (ritual bath)"
    ]
  }
];

// Monthly Islamic observances
export const monthlyObservances = [
  {
    days: "13, 14, 15",
    name: "Al-Ayyam al-Bid (White Days)",
    arabicName: "الأيام البيض",
    significance: "Three days each lunar month when fasting is especially recommended",
    recommendations: [
      "Fast on the 13th, 14th, and 15th of each lunar month",
      "Increase dhikr and worship",
      "Give charity",
      "Make du'a and istighfar"
    ]
  }
];

export const getUpcomingEvents = (currentDate: Date) => {
  // This would normally calculate based on current Hijri date
  // For demonstration, returning a few upcoming events
  return islamicEvents.slice(0, 5);
};