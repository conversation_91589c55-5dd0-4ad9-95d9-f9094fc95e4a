<?xml version="1.0" encoding="utf-8"?>
<merger version="3"><dataSet config=":capacitor-cordova-android-plugins" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\Desktop\IslamicCompanion\android\capacitor-cordova-android-plugins\build\intermediates\library_assets\debug\packageDebugAssets\out"/></dataSet><dataSet config=":capacitor-android" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\Desktop\IslamicCompanion\node_modules\@capacitor\android\capacitor\build\intermediates\library_assets\debug\packageDebugAssets\out"><file name="native-bridge.js" path="C:\Users\<USER>\Desktop\IslamicCompanion\node_modules\@capacitor\android\capacitor\build\intermediates\library_assets\debug\packageDebugAssets\out\native-bridge.js"/></source></dataSet><dataSet config="main" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\Desktop\IslamicCompanion\android\app\src\main\assets"><file name="capacitor.config.json" path="C:\Users\<USER>\Desktop\IslamicCompanion\android\app\src\main\assets\capacitor.config.json"/><file name="capacitor.plugins.json" path="C:\Users\<USER>\Desktop\IslamicCompanion\android\app\src\main\assets\capacitor.plugins.json"/><file name="public/assets/index-B_6Fx5zN.js" path="C:\Users\<USER>\Desktop\IslamicCompanion\android\app\src\main\assets\public\assets\index-B_6Fx5zN.js"/><file name="public/assets/index-Dpm40_X0.css" path="C:\Users\<USER>\Desktop\IslamicCompanion\android\app\src\main\assets\public\assets\index-Dpm40_X0.css"/><file name="public/cordova.js" path="C:\Users\<USER>\Desktop\IslamicCompanion\android\app\src\main\assets\public\cordova.js"/><file name="public/cordova_plugins.js" path="C:\Users\<USER>\Desktop\IslamicCompanion\android\app\src\main\assets\public\cordova_plugins.js"/><file name="public/index.html" path="C:\Users\<USER>\Desktop\IslamicCompanion\android\app\src\main\assets\public\index.html"/><file name="public/manifest.json" path="C:\Users\<USER>\Desktop\IslamicCompanion\android\app\src\main\assets\public\manifest.json"/></source></dataSet><dataSet config="debug" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\Desktop\IslamicCompanion\android\app\src\debug\assets"/></dataSet><dataSet config="generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\Desktop\IslamicCompanion\android\app\build\intermediates\shader_assets\debug\compileDebugShaders\out"/></dataSet></merger>