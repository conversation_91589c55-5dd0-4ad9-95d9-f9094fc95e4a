import { useState, useEffect } from "react";
import { Search, Book, Filter, ArrowLeft } from "lucide-react";
import { <PERSON><PERSON> } from "./ui/button";
import { Card } from "./ui/card";
import { Input } from "./ui/input";
import { Badge } from "./ui/badge";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "./ui/select";
import { completeQuran } from "../data/complete-quran-114";

interface QuranBrowserProps {
  onBack?: () => void;
  language?: "en" | "ar";
}

export function QuranBrowser({ onBack, language = "en" }: QuranBrowserProps) {
  const [searchTerm, setSearchTerm] = useState("");
  const [selectedType, setSelectedType] = useState<"all" | "Meccan" | "Medinan">("all");
  const [filteredSurahs, setFilteredSurahs] = useState(completeQuran);
  const [selectedSurah, setSelectedSurah] = useState<number | null>(null);

  useEffect(() => {
    let filtered = completeQuran;

    // Filter by type
    if (selectedType !== "all") {
      filtered = filtered.filter(surah => surah.type === selectedType);
    }

    // Filter by search term
    if (searchTerm) {
      filtered = filtered.filter(surah => 
        surah.name.includes(searchTerm) ||
        surah.englishName.toLowerCase().includes(searchTerm.toLowerCase()) ||
        surah.meaning.toLowerCase().includes(searchTerm.toLowerCase())
      );
    }

    setFilteredSurahs(filtered);
  }, [searchTerm, selectedType]);

  const renderSurahView = () => {
    const surah = completeQuran.find(s => s.number === selectedSurah);
    if (!surah) return null;

    return (
      <div className="space-y-4">
        <div className="flex items-center gap-3 mb-6">
          <Button
            variant="ghost"
            size="sm"
            onClick={() => setSelectedSurah(null)}
            className="flex items-center gap-2"
          >
            <ArrowLeft className="h-4 w-4" />
            العودة للفهرس
          </Button>
        </div>

        <div className="text-center mb-6">
          <h2 className="text-2xl font-bold text-emerald-800 dark:text-emerald-200 mb-2">
            {surah.name}
          </h2>
          <p className="text-lg text-gray-600 dark:text-gray-300 mb-1">
            {surah.englishName} - {surah.meaning}
          </p>
          <div className="flex justify-center items-center gap-3">
            <Badge variant={surah.type === "Meccan" ? "default" : "secondary"}>
              {surah.type === "Meccan" ? "مكية" : "مدنية"}
            </Badge>
            <span className="text-sm text-gray-500">
              {surah.ayahs} آية
            </span>
          </div>
        </div>

        <div className="space-y-4">
          {surah.verses.map((verse) => (
            <Card key={verse.number} className="p-4 enhanced-card">
              <div className="space-y-3">
                <div className="flex items-start justify-between">
                  <Badge variant="outline" className="text-xs">
                    {verse.number}
                  </Badge>
                </div>
                
                <div className="text-right">
                  <p className="text-xl leading-loose arabic-text calligraphy text-gray-800 dark:text-white">
                    {verse.arabic}
                  </p>
                </div>
                
                <div className="bg-gray-50 dark:bg-gray-800 p-3 rounded-lg">
                  <p className="text-sm italic text-gray-700 dark:text-gray-300 mb-2">
                    {verse.transliteration}
                  </p>
                  <p className="text-sm text-gray-700 dark:text-gray-300">
                    {verse.translation}
                  </p>
                </div>
              </div>
            </Card>
          ))}
        </div>
      </div>
    );
  };

  const renderSurahList = () => (
    <div className="space-y-4">
      {/* Search and Filters */}
      <div className="space-y-3">
        <div className="relative">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
          <Input
            placeholder={language === "ar" ? "ابحث في السور..." : "Search surahs..."}
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="pl-10"
          />
        </div>
        
        <Select value={selectedType} onValueChange={(value: "all" | "Meccan" | "Medinan") => setSelectedType(value)}>
          <SelectTrigger>
            <SelectValue placeholder={language === "ar" ? "نوع السورة" : "Surah type"} />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">
              {language === "ar" ? "جميع السور" : "All Surahs"}
            </SelectItem>
            <SelectItem value="Meccan">
              {language === "ar" ? "مكية" : "Meccan"}
            </SelectItem>
            <SelectItem value="Medinan">
              {language === "ar" ? "مدنية" : "Medinan"}
            </SelectItem>
          </SelectContent>
        </Select>
      </div>

      {/* Statistics */}
      <div className="grid grid-cols-3 gap-3 mb-6">
        <div className="text-center p-3 bg-emerald-50 dark:bg-emerald-900/20 rounded-lg">
          <Book className="h-5 w-5 text-emerald-600 dark:text-emerald-400 mx-auto mb-1" />
          <p className="text-sm font-medium text-emerald-800 dark:text-emerald-200">
            {filteredSurahs.length}
          </p>
          <p className="text-xs text-emerald-600 dark:text-emerald-400">
            سورة
          </p>
        </div>
        <div className="text-center p-3 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
          <Filter className="h-5 w-5 text-blue-600 dark:text-blue-400 mx-auto mb-1" />
          <p className="text-sm font-medium text-blue-800 dark:text-blue-200">
            {filteredSurahs.filter(s => s.type === "Meccan").length}
          </p>
          <p className="text-xs text-blue-600 dark:text-blue-400">
            مكية
          </p>
        </div>
        <div className="text-center p-3 bg-purple-50 dark:bg-purple-900/20 rounded-lg">
          <Filter className="h-5 w-5 text-purple-600 dark:text-purple-400 mx-auto mb-1" />
          <p className="text-sm font-medium text-purple-800 dark:text-purple-200">
            {filteredSurahs.filter(s => s.type === "Medinan").length}
          </p>
          <p className="text-xs text-purple-600 dark:text-purple-400">
            مدنية
          </p>
        </div>
      </div>

      {/* Surah List */}
      <div className="grid gap-3">
        {filteredSurahs.map((surah) => (
          <Card 
            key={surah.number} 
            className="p-4 cursor-pointer enhanced-card hover:bg-gray-50 dark:hover:bg-gray-800"
            onClick={() => setSelectedSurah(surah.number)}
          >
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-3">
                <div className="w-10 h-10 rounded-full bg-emerald-100 dark:bg-emerald-900/20 flex items-center justify-center">
                  <span className="text-sm font-bold text-emerald-700 dark:text-emerald-300">
                    {surah.number}
                  </span>
                </div>
                <div>
                  <h3 className="font-semibold text-gray-800 dark:text-white">
                    {surah.name}
                  </h3>
                  <p className="text-sm text-gray-600 dark:text-gray-300">
                    {surah.englishName} - {surah.meaning}
                  </p>
                </div>
              </div>
              <div className="text-right">
                <Badge variant={surah.type === "Meccan" ? "default" : "secondary"} className="mb-1">
                  {surah.type === "Meccan" ? "مكية" : "مدنية"}
                </Badge>
                <p className="text-xs text-gray-500">
                  {surah.ayahs} آية
                </p>
              </div>
            </div>
          </Card>
        ))}
      </div>

      {filteredSurahs.length === 0 && (
        <div className="text-center py-8 text-gray-500 dark:text-gray-400">
          <Book className="h-12 w-12 mx-auto mb-3 opacity-50" />
          <p>لا توجد سور تطابق البحث</p>
        </div>
      )}
    </div>
  );

  return (
    <section className="p-3 sm:p-4 w-full">
      <Card className="p-4 sm:p-6 mx-auto max-w-2xl">
        {onBack && selectedSurah === null && (
          <div className="flex justify-between items-center mb-4">
            <Button variant="ghost" size="sm" onClick={onBack} className="mr-2">
              <ArrowLeft className="h-4 w-4" />
            </Button>
            <h2 className="text-lg sm:text-xl font-semibold text-gray-800 dark:text-white text-center flex-1">
              {language === "ar" ? "القرآن الكريم الكامل" : "Complete Holy Quran"}
            </h2>
            <div className="w-8"></div>
          </div>
        )}

        {selectedSurah ? renderSurahView() : renderSurahList()}
      </Card>
    </section>
  );
}