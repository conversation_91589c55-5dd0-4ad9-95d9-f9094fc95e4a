export interface Hadith {
  id: string;
  arabic: string;
  translation: string;
  narrator: string;
  source: string;
  category: 'faith' | 'prayer' | 'charity' | 'fasting' | 'hajj' | 'manners' | 'knowledge' | 'general';
  grade: 'sahih' | 'hasan' | 'daif';
  chapter: string;
}

export const hadithData: Hadith[] = [
  {
    id: "faith-1",
    arabic: "عن أبي هريرة رضي الله عنه قال: قال رسول الله صلى الله عليه وسلم: \"الإيمان بضع وسبعون شعبة، فأفضلها قول لا إله إلا الله، وأدناها إماطة الأذى عن الطريق، والحياء شعبة من الإيمان\"",
    translation: "<PERSON> Huraira reported: The Messenger of Allah, peace and blessings be upon him, said: \"Faith has over seventy branches. The most excellent of which is the declaration that there is no god but Allah, and the humblest of which is the removal of what is injurious from the path, and modesty is a branch of faith.\"",
    narrator: "أبو هريرة",
    source: "صحيح البخاري",
    category: "faith",
    grade: "sahih",
    chapter: "كتاب الإيمان"
  },
  {
    id: "prayer-1",
    arabic: "عن أبي هريرة رضي الله عنه قال: سمعت رسول الله صلى الله عليه وسلم يقول: \"أرأيتم لو أن نهراً بباب أحدكم يغتسل منه كل يوم خمس مرات، هل يبقى من درنه شيء؟\" قالوا: لا يبقى من درنه شيء. قال: \"فذلك مثل الصلوات الخمس، يمحو الله بهن الخطايا\"",
    translation: "Abu Huraira reported: I heard the Messenger of Allah, peace and blessings be upon him, saying: \"Tell me, if there was a river at the door of one of you in which he bathed five times a day, would any filth remain on him?\" They said: No filth would remain on him. He said: \"That is the example of the five prayers. Allah wipes away sins through them.\"",
    narrator: "أبو هريرة",
    source: "صحيح البخاري",
    category: "prayer",
    grade: "sahih",
    chapter: "كتاب الصلاة"
  },
  {
    id: "charity-1",
    arabic: "عن أبي هريرة رضي الله عنه قال: قال رسول الله صلى الله عليه وسلم: \"ما تصدق أحد بصدقة من طيب، ولا يقبل الله إلا الطيب، إلا أخذها الرحمن بيمينه، وإن كانت تمرة، فتربو في كف الرحمن حتى تكون أعظم من الجبل، كما يربي أحدكم فلوه أو فصيله\"",
    translation: "Abu Huraira reported: The Messenger of Allah, peace and blessings be upon him, said: \"No one gives charity from good earnings, and Allah only accepts what is good, but the Merciful takes it with His right hand. Even if it is a date, it grows in the palm of the Merciful until it becomes greater than a mountain, just as one of you raises his foal or young camel.\"",
    narrator: "أبو هريرة",
    source: "صحيح البخاري",
    category: "charity",
    grade: "sahih",
    chapter: "كتاب الزكاة"
  },
  {
    id: "manners-1",
    arabic: "عن أبي هريرة رضي الله عنه قال: قال رسول الله صلى الله عليه وسلم: \"المؤمن مرآة المؤمن، والمؤمن أخو المؤمن، يكف عليه ضيعته، ويحوطه من ورائه\"",
    translation: "Abu Huraira reported: The Messenger of Allah, peace and blessings be upon him, said: \"The believer is a mirror to the believer, and the believer is the brother of the believer. He protects him from loss and guards his back.\"",
    narrator: "أبو هريرة",
    source: "سنن أبي داود",
    category: "manners",
    grade: "hasan",
    chapter: "كتاب الأدب"
  },
  {
    id: "knowledge-1",
    arabic: "عن أبي هريرة رضي الله عنه قال: قال رسول الله صلى الله عليه وسلم: \"من سلك طريقاً يلتمس فيه علماً، سهل الله له به طريقاً إلى الجنة\"",
    translation: "Abu Huraira reported: The Messenger of Allah, peace and blessings be upon him, said: \"Whoever travels a path in search of knowledge, Allah will make easy for him a path to Paradise.\"",
    narrator: "أبو هريرة",
    source: "صحيح مسلم",
    category: "knowledge",
    grade: "sahih",
    chapter: "كتاب العلم"
  },
  {
    id: "fasting-1",
    arabic: "عن أبي هريرة رضي الله عنه قال: قال رسول الله صلى الله عليه وسلم: \"قال الله عز وجل: كل عمل ابن آدم له إلا الصيام فإنه لي وأنا أجزي به\"",
    translation: "Abu Huraira reported: The Messenger of Allah, peace and blessings be upon him, said: \"Allah the Almighty said: Every deed of the son of Adam is for him, except fasting. It is for Me and I will reward it.\"",
    narrator: "أبو هريرة",
    source: "صحيح البخاري",
    category: "fasting",
    grade: "sahih",
    chapter: "كتاب الصيام"
  },
  {
    id: "general-1",
    arabic: "عن النعمان بن بشير رضي الله عنهما قال: سمعت رسول الله صلى الله عليه وسلم يقول: \"الحلال بين، والحرام بين، وبينهما مشتبهات لا يعلمهن كثير من الناس، فمن اتقى الشبهات استبرأ لدينه وعرضه\"",
    translation: "Al-Nu'man ibn Bashir reported: I heard the Messenger of Allah, peace and blessings be upon him, say: \"The lawful is clear and the unlawful is clear, and between them are doubtful matters that many people do not know. Whoever avoids the doubtful matters has protected his religion and honor.\"",
    narrator: "النعمان بن بشير",
    source: "صحيح البخاري",
    category: "general",
    grade: "sahih",
    chapter: "كتاب الإيمان"
  },
  {
    id: "hajj-1",
    arabic: "عن أبي هريرة رضي الله عنه قال: قال رسول الله صلى الله عليه وسلم: \"الحج المبرور ليس له جزاء إلا الجنة\"",
    translation: "Abu Huraira reported: The Messenger of Allah, peace and blessings be upon him, said: \"An accepted Hajj has no reward except Paradise.\"",
    narrator: "أبو هريرة",
    source: "صحيح البخاري",
    category: "hajj",
    grade: "sahih",
    chapter: "كتاب الحج"
  }
];

export const hadithCategories = [
  { id: 'faith', name: 'الإيمان', englishName: 'Faith' },
  { id: 'prayer', name: 'الصلاة', englishName: 'Prayer' },
  { id: 'charity', name: 'الزكاة', englishName: 'Charity' },
  { id: 'fasting', name: 'الصيام', englishName: 'Fasting' },
  { id: 'hajj', name: 'الحج', englishName: 'Hajj' },
  { id: 'manners', name: 'الأخلاق', englishName: 'Manners' },
  { id: 'knowledge', name: 'العلم', englishName: 'Knowledge' },
  { id: 'general', name: 'عام', englishName: 'General' }
];

export const hadithSources = [
  { id: 'bukhari', name: 'صحيح البخاري', englishName: 'Sahih al-Bukhari' },
  { id: 'muslim', name: 'صحيح مسلم', englishName: 'Sahih Muslim' },
  { id: 'abu-dawood', name: 'سنن أبي داود', englishName: 'Sunan Abu Dawood' },
  { id: 'tirmidhi', name: 'سنن الترمذي', englishName: 'Sunan at-Tirmidhi' },
  { id: 'nasai', name: 'سنن النسائي', englishName: 'Sunan an-Nasa\'i' },
  { id: 'ibn-majah', name: 'سنن ابن ماجه', englishName: 'Sunan Ibn Majah' }
];