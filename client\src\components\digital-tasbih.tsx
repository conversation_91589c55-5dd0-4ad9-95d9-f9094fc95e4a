import { useState, useEffect } from "react";
import { Card } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import { apiRequest } from "@/lib/queryClient";

interface DhikrType {
  id: string;
  arabic: string;
  transliteration: string;
  english: string;
  target: number;
}

const dhikrTypes: DhikrType[] = [
  {
    id: "subhanallah",
    arabic: "سبحان الله",
    transliteration: "SubhanAllah",
    english: "Glory be to Allah",
    target: 33,
  },
  {
    id: "alhamdulillah",
    arabic: "الحمد لله",
    transliteration: "Alham<PERSON><PERSON><PERSON>",
    english: "Praise be to Allah",
    target: 33,
  },
  {
    id: "allahu-akbar",
    arabic: "الله أكبر",
    transliteration: "<PERSON><PERSON> <PERSON>",
    english: "<PERSON> is Greatest",
    target: 34,
  },
  {
    id: "astagh<PERSON><PERSON><PERSON>",
    arabic: "استغفر الله",
    transliteration: "Astagh<PERSON><PERSON>llah",
    english: "I seek forgiveness from Allah",
    target: 100,
  },
];

// Mock user ID for demo
const DEMO_USER_ID = 1;

export function DigitalTasbih() {
  const [selectedDhikr, setSelectedDhikr] = useState<string>("subhanallah");
  const [target, setTarget] = useState<number>(33);
  const queryClient = useQueryClient();

  const currentDhikr = dhikrTypes.find(d => d.id === selectedDhikr);

  const { data: dhikrCounts = [] } = useQuery({
    queryKey: ["/api/dhikr", DEMO_USER_ID, new Date().toDateString()],
    queryFn: async () => {
      const res = await fetch(`/api/dhikr/${DEMO_USER_ID}?date=${new Date().toISOString()}`);
      if (!res.ok) throw new Error('Failed to fetch dhikr counts');
      return res.json();
    },
  });

  const currentCount = dhikrCounts.find((d: any) => d.dhikrType === selectedDhikr)?.count || 0;

  const incrementMutation = useMutation({
    mutationFn: async () => {
      await apiRequest("POST", "/api/dhikr", {
        userId: DEMO_USER_ID,
        dhikrType: selectedDhikr,
        count: 1,
        date: new Date(),
      });
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ 
        queryKey: ["/api/dhikr", DEMO_USER_ID] 
      });
      
      // Vibration feedback
      if (navigator.vibrate) {
        navigator.vibrate(50);
      }
    },
  });

  const resetMutation = useMutation({
    mutationFn: async () => {
      await apiRequest("POST", "/api/dhikr", {
        userId: DEMO_USER_ID,
        dhikrType: selectedDhikr,
        count: -currentCount,
        date: new Date(),
      });
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ 
        queryKey: ["/api/dhikr", DEMO_USER_ID] 
      });
    },
  });

  const progress = Math.min((currentCount / target) * 100, 100);
  const strokeDasharray = 283; // 2 * π * 45
  const strokeDashoffset = strokeDasharray - (strokeDasharray * progress) / 100;

  const handleTap = () => {
    incrementMutation.mutate();
  };

  const handleReset = () => {
    resetMutation.mutate();
  };

  const handleSetTarget = () => {
    const newTarget = prompt("Enter new target:", target.toString());
    if (newTarget && !isNaN(parseInt(newTarget))) {
      setTarget(parseInt(newTarget));
    }
  };

  useEffect(() => {
    const selected = dhikrTypes.find(d => d.id === selectedDhikr);
    if (selected) {
      setTarget(selected.target);
    }
  }, [selectedDhikr]);

  return (
    <section className="p-3 sm:p-4 w-full">
      <Card className="p-4 sm:p-6 mx-auto max-w-lg">
        <h2 className="text-lg sm:text-xl font-semibold text-gray-800 dark:text-white mb-4 sm:mb-6 text-center">
          Digital Tasbih
        </h2>

        {/* Dhikr Selection */}
        <div className="mb-4 sm:mb-6">
          <div className="grid grid-cols-2 gap-2 sm:gap-3">
            {dhikrTypes.map((dhikr) => (
              <Button
                key={dhikr.id}
                variant={selectedDhikr === dhikr.id ? "default" : "outline"}
                className={`p-2 sm:p-3 h-auto text-xs sm:text-sm font-medium touch-manipulation ${
                  selectedDhikr === dhikr.id 
                    ? "bg-emerald-600 hover:bg-emerald-700 text-white" 
                    : ""
                }`}
                onClick={() => setSelectedDhikr(dhikr.id)}
              >
                <div className="text-center">
                  <div className="font-amiri text-sm sm:text-base">{dhikr.arabic}</div>
                  <div className="text-xs opacity-80 truncate">{dhikr.transliteration}</div>
                </div>
              </Button>
            ))}
          </div>
        </div>

        {/* Counter Display */}
        <div className="text-center mb-6 sm:mb-8">
          <div className="relative w-40 h-40 sm:w-48 sm:h-48 mx-auto mb-4">
            {/* Progress Ring */}
            <svg className="w-full h-full transform -rotate-90" viewBox="0 0 100 100">
              <circle 
                cx="50" 
                cy="50" 
                r="45" 
                stroke="currentColor" 
                strokeWidth="2" 
                fill="none" 
                className="text-gray-200 dark:text-gray-700"
              />
              <circle 
                cx="50" 
                cy="50" 
                r="45" 
                stroke="currentColor" 
                strokeWidth="2" 
                fill="none" 
                strokeLinecap="round" 
                className="text-emerald-600 dark:text-emerald-400 transition-all duration-300"
                style={{ 
                  strokeDasharray: strokeDasharray, 
                  strokeDashoffset: strokeDashoffset 
                }}
              />
            </svg>
            {/* Counter Number */}
            <div className="absolute inset-0 flex items-center justify-center">
              <span className="text-3xl sm:text-4xl font-bold text-emerald-600 dark:text-emerald-400">
                {currentCount}
              </span>
            </div>
          </div>
          
          {/* Target Info */}
          <p className="text-sm text-gray-600 dark:text-gray-400">
            {currentCount} / {target} (Target)
          </p>
          
          {currentCount >= target && (
            <Badge className="mt-2 bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200 text-xs">
              Target Completed!
            </Badge>
          )}
        </div>

        {/* Current Dhikr Display */}
        {currentDhikr && (
          <div className="text-center mb-6 p-4 bg-gray-50 dark:bg-gray-800 rounded-xl">
            <p className="text-xl font-amiri text-gray-800 dark:text-white mb-1">
              {currentDhikr.arabic}
            </p>
            <p className="text-sm text-gray-600 dark:text-gray-400">
              {currentDhikr.english}
            </p>
          </div>
        )}

        {/* Tap Button */}
        <div className="text-center mb-4 sm:mb-6">
          <Button
            onClick={handleTap}
            disabled={incrementMutation.isPending}
            className="w-28 h-28 sm:w-32 sm:h-32 bg-gradient-to-br from-emerald-600 to-blue-600 hover:from-emerald-700 hover:to-blue-700 text-white rounded-full text-lg sm:text-xl font-semibold shadow-lg active:scale-95 transition-transform touch-manipulation"
          >
            {incrementMutation.isPending ? "..." : "TAP"}
          </Button>
        </div>

        {/* Action Buttons */}
        <div className="flex space-x-3">
          <Button
            variant="outline"
            className="flex-1"
            onClick={handleReset}
            disabled={resetMutation.isPending || currentCount === 0}
          >
            Reset
          </Button>
          <Button
            className="flex-1 bg-emerald-600 hover:bg-emerald-700 text-white"
            onClick={handleSetTarget}
          >
            Set Target
          </Button>
        </div>
      </Card>
    </section>
  );
}
