import { useState, useEffect } from "react";
import { Card } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { useQuery } from "@tanstack/react-query";
import { getPrayerTimes, type PrayerTimesResult } from "@/lib/prayer-times";
import { useLocation } from "@/hooks/use-location";
import { getHijriDate } from "@/lib/hijri-calendar";

export function PrayerTimes() {
  const { location, loading: locationLoading } = useLocation();
  const [currentTime, setCurrentTime] = useState(new Date());

  const { data: prayerTimes, isLoading } = useQuery({
    queryKey: ["/api/prayer-times", location?.latitude, location?.longitude],
    queryFn: async () => {
      if (!location) return null;
      return getPrayerTimes(location.latitude, location.longitude);
    },
    enabled: !!location,
  });

  useEffect(() => {
    const timer = setInterval(() => {
      setCurrentTime(new Date());
    }, 1000);
    return () => clearInterval(timer);
  }, []);

  const getNextPrayer = (times: PrayerTimesResult) => {
    const now = new Date();
    const prayers = [
      { name: "Fajr", time: times.fajr, arabicName: "الفجر" },
      { name: "Sunrise", time: times.sunrise, arabicName: "الشروق" },
      { name: "Dhuhr", time: times.dhuhr, arabicName: "الظهر" },
      { name: "Asr", time: times.asr, arabicName: "العصر" },
      { name: "Maghrib", time: times.maghrib, arabicName: "المغرب" },
      { name: "Isha", time: times.isha, arabicName: "العشاء" },
    ];

    for (const prayer of prayers) {
      if (prayer.time > now) {
        const diff = prayer.time.getTime() - now.getTime();
        const hours = Math.floor(diff / (1000 * 60 * 60));
        const minutes = Math.floor((diff % (1000 * 60 * 60)) / (1000 * 60));
        const seconds = Math.floor((diff % (1000 * 60)) / 1000);
        
        return {
          ...prayer,
          timeRemaining: `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`
        };
      }
    }

    // If all prayers have passed, next prayer is tomorrow's Fajr
    const tomorrowFajr = new Date(times.fajr);
    tomorrowFajr.setDate(tomorrowFajr.getDate() + 1);
    const diff = tomorrowFajr.getTime() - now.getTime();
    const hours = Math.floor(diff / (1000 * 60 * 60));
    const minutes = Math.floor((diff % (1000 * 60 * 60)) / (1000 * 60));
    const seconds = Math.floor((diff % (1000 * 60)) / 1000);

    return {
      name: "Fajr",
      arabicName: "الفجر",
      time: tomorrowFajr,
      timeRemaining: `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`
    };
  };

  const formatTime = (date: Date) => {
    return date.toLocaleTimeString('en-US', { 
      hour: '2-digit', 
      minute: '2-digit',
      hour12: false 
    });
  };

  if (locationLoading || isLoading) {
    return (
      <section className="p-4">
        <div className="bg-gradient-to-r from-emerald-600 to-blue-600 rounded-2xl text-white p-6 animate-pulse">
          <div className="h-6 bg-white bg-opacity-20 rounded mb-4"></div>
          <div className="h-8 bg-white bg-opacity-20 rounded mb-2"></div>
          <div className="h-6 bg-white bg-opacity-20 rounded mb-6"></div>
          <div className="grid grid-cols-3 gap-3">
            {Array.from({ length: 6 }).map((_, i) => (
              <div key={i} className="h-16 bg-white bg-opacity-20 rounded-xl"></div>
            ))}
          </div>
        </div>
      </section>
    );
  }

  if (!prayerTimes) {
    return (
      <section className="p-4">
        <Card className="p-6 text-center">
          <p className="text-gray-600 dark:text-gray-400">
            Unable to get location for prayer times calculation.
          </p>
        </Card>
      </section>
    );
  }

  const nextPrayer = getNextPrayer(prayerTimes);
  const hijriDate = getHijriDate(new Date());

  const prayers = [
    { name: "Fajr", time: prayerTimes.fajr, arabicName: "الفجر" },
    { name: "Sunrise", time: prayerTimes.sunrise, arabicName: "الشروق" },
    { name: "Dhuhr", time: prayerTimes.dhuhr, arabicName: "الظهر" },
    { name: "Asr", time: prayerTimes.asr, arabicName: "العصر" },
    { name: "Maghrib", time: prayerTimes.maghrib, arabicName: "المغرب" },
    { name: "Isha", time: prayerTimes.isha, arabicName: "العشاء" },
  ];

  return (
    <section className="p-3 sm:p-4 w-full">
      <div className="bg-gradient-to-r from-emerald-600 to-blue-600 dark:from-emerald-700 dark:to-blue-700 rounded-xl sm:rounded-2xl text-white p-4 sm:p-6 mb-4 mx-auto max-w-lg">
        <div className="flex justify-between items-center mb-4">
          <h2 className="text-base sm:text-lg font-semibold">Prayer Times</h2>
          <Badge variant="secondary" className="bg-white bg-opacity-20 text-white text-xs">
            {hijriDate}
          </Badge>
        </div>
        
        {/* Next Prayer Countdown */}
        <div className="text-center mb-6">
          <p className="text-xs sm:text-sm opacity-90">Next Prayer</p>
          <h3 className="text-xl sm:text-2xl font-bold font-amiri">
            {nextPrayer.arabicName} ({nextPrayer.name})
          </h3>
          <p className="text-base sm:text-lg font-mono">{nextPrayer.timeRemaining}</p>
        </div>

        {/* Prayer Times Grid */}
        <div className="grid grid-cols-3 gap-2 sm:gap-3">
          {prayers.map((prayer) => {
            const isNext = prayer.name === nextPrayer.name;
            return (
              <div
                key={prayer.name}
                className={`text-center p-2 sm:p-3 rounded-lg sm:rounded-xl touch-manipulation ${
                  isNext 
                    ? "bg-white bg-opacity-30 border-2 border-white" 
                    : "bg-white bg-opacity-20"
                }`}
              >
                <p className="text-xs opacity-80 truncate">{prayer.name}</p>
                <p className="font-semibold text-sm sm:text-base">{formatTime(prayer.time)}</p>
              </div>
            );
          })}
        </div>
      </div>
    </section>
  );
}
