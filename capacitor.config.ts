import { CapacitorConfig } from '@capacitor/cli';

const config: CapacitorConfig = {
  appId: 'com.islamicapp.complete',
  appName: 'التطبيق الإسلامي الشامل',
  webDir: 'client/dist',
  server: {
    androidScheme: 'https'
  },
  plugins: {
    SplashScreen: {
      launchShowDuration: 3000,
      launchAutoHide: true,
      launchFadeOutDuration: 3000,
      backgroundColor: "#10b981",
      androidSplashResourceName: "splash",
      androidScaleType: "CENTER_CROP",
      showSpinner: true,
      androidSpinnerStyle: "large",
      iosSpinnerStyle: "small",
      spinnerColor: "#ffffff",
      splashFullScreen: true,
      splashImmersive: true,
      layoutName: "launch_screen",
      useDialog: true,
    },
    LocalNotifications: {
      smallIcon: "ic_stat_icon_config_sample",
      iconColor: "#10b981",
      sound: "beep.wav",
    },
    Geolocation: {
      permissions: ["ACCESS_COARSE_LOCATION", "ACCESS_FINE_LOCATION"]
    },
    Device: {},
    StatusBar: {
      style: 'LIGHT',
      backgroundColor: '#10b981'
    },
    Keyboard: {
      resize: 'body'
    },
    Haptics: {},
    Storage: {},
    Network: {},
    Share: {},
    Toast: {},
    ScreenOrientation: {
      orientations: ['portrait', 'portrait-upside-down']
    }
  },
  android: {
    allowMixedContent: true,
    captureInput: true,
    webContentsDebuggingEnabled: true,
    backgroundColor: "#10b981"
  },
  ios: {
    contentInset: 'automatic',
    backgroundColor: "#10b981"
  }
};

export default config;