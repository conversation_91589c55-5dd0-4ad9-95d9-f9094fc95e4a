@tailwind base;
@tailwind components;
@tailwind utilities;

/* Quran Page Styles */
.quran-page-container {
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
}

.quran-page-image {
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
  transition: transform 0.2s ease;
}

.quran-page-image:hover {
  transform: scale(1.02);
}

.page-navigation {
  backdrop-filter: blur(10px);
  background: rgba(255, 255, 255, 0.9);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.dark .page-navigation {
  background: rgba(0, 0, 0, 0.8);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

/* Enhanced Reading Mode Styles */
:root {
  --reading-font-size: 16px;
  --reading-line-height: 1.6;
  --reading-font-family: '<PERSON><PERSON>', serif;
}

.reading-content {
  font-size: var(--reading-font-size);
  line-height: var(--reading-line-height);
  font-family: var(--reading-font-family);
  transition: all 0.3s ease;
}

/* High Contrast Mode */
.high-contrast {
  --background: 255 255 255;
  --foreground: 0 0 0;
  --card: 255 255 255;
  --card-foreground: 0 0 0;
  --popover: 255 255 255;
  --popover-foreground: 0 0 0;
  --primary: 0 0 0;
  --primary-foreground: 255 255 255;
  --secondary: 240 240 240;
  --secondary-foreground: 0 0 0;
  --muted: 240 240 240;
  --muted-foreground: 64 64 64;
  --accent: 240 240 240;
  --accent-foreground: 0 0 0;
  --destructive: 255 0 0;
  --destructive-foreground: 255 255 255;
  --border: 0 0 0;
  --input: 240 240 240;
  --ring: 0 0 0;
}

/* Night Mode for Reading */
.night-mode {
  --background: 15 15 15;
  --foreground: 240 240 240;
  --card: 20 20 20;
  --card-foreground: 240 240 240;
  --popover: 20 20 20;
  --popover-foreground: 240 240 240;
  --primary: 240 240 240;
  --primary-foreground: 15 15 15;
  --secondary: 40 40 40;
  --secondary-foreground: 240 240 240;
  --muted: 40 40 40;
  --muted-foreground: 180 180 180;
  --accent: 40 40 40;
  --accent-foreground: 240 240 240;
  --destructive: 255 85 85;
  --destructive-foreground: 240 240 240;
  --border: 60 60 60;
  --input: 40 40 40;
  --ring: 180 180 180;
}

/* Enhanced Scrollbar */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: hsl(var(--muted));
  border-radius: 4px;
}

::-webkit-scrollbar-thumb {
  background: hsl(var(--muted-foreground) / 0.5);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: hsl(var(--muted-foreground) / 0.7);
}

/* Enhanced Button Hover Effects */
.enhanced-button {
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  transform-origin: center;
}

.enhanced-button:hover {
  transform: scale(1.02);
}

.enhanced-button:active {
  transform: scale(0.98);
}

/* Improved Arabic Typography */
.arabic-text {
  font-family: 'Amiri', 'Noto Sans Arabic', serif;
  font-feature-settings: 'liga', 'dlig', 'calt';
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* Enhanced Focus Indicators */
.enhanced-focus:focus-visible {
  outline: 2px solid hsl(var(--ring));
  outline-offset: 2px;
  border-radius: 4px;
}

/* Loading Animations */
@keyframes shimmer {
  0% {
    background-position: -200px 0;
  }
  100% {
    background-position: calc(200px + 100%) 0;
  }
}

.shimmer {
  background: linear-gradient(
    90deg,
    hsl(var(--muted)) 25%,
    hsl(var(--muted-foreground) / 0.1) 50%,
    hsl(var(--muted)) 75%
  );
  background-size: 200px 100%;
  animation: shimmer 1.5s infinite;
}

/* Enhanced Card Animations */
.enhanced-card {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.enhanced-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px hsl(var(--foreground) / 0.1);
}

/* Smooth Page Transitions */
.page-transition-enter {
  opacity: 0;
  transform: translateX(20px);
}

.page-transition-enter-active {
  opacity: 1;
  transform: translateX(0);
  transition: all 0.3s ease-out;
}

.page-transition-exit {
  opacity: 1;
  transform: translateX(0);
}

.page-transition-exit-active {
  opacity: 0;
  transform: translateX(-20px);
  transition: all 0.3s ease-in;
}

/* Enhanced Floating Action Buttons */
.fab {
  position: fixed;
  bottom: 80px;
  right: 16px;
  width: 56px;
  height: 56px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4px 12px hsl(var(--foreground) / 0.15);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  z-index: 1000;
}

.fab:hover {
  transform: scale(1.1);
  box-shadow: 0 6px 20px hsl(var(--foreground) / 0.2);
}

/* Prayer Time Progress Indicator */
.prayer-progress {
  background: linear-gradient(
    90deg,
    hsl(var(--primary)) 0%,
    hsl(var(--primary) / 0.8) 50%,
    hsl(var(--primary) / 0.6) 100%
  );
  background-size: 200% 100%;
  animation: prayer-glow 3s ease-in-out infinite;
}

@keyframes prayer-glow {
  0%, 100% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
}

/* Enhanced Typography Scale */
.text-display {
  font-size: clamp(2rem, 5vw, 4rem);
  line-height: 1.1;
  letter-spacing: -0.025em;
}

.text-title {
  font-size: clamp(1.5rem, 4vw, 2.5rem);
  line-height: 1.2;
  letter-spacing: -0.02em;
}

/* Islamic Calligraphy Enhancements */
.calligraphy {
  font-family: 'Amiri Quran', 'Amiri', serif;
  font-weight: 400;
  line-height: 2;
  text-align: justify;
  hyphens: auto;
  word-spacing: 0.1em;
}

/* Responsive Grid Improvements */
.responsive-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 1rem;
}

@media (min-width: 768px) {
  .responsive-grid {
    gap: 1.5rem;
  }
}

/* Enhanced Input Styles */
.enhanced-input {
  transition: all 0.2s ease;
  border: 2px solid transparent;
}

.enhanced-input:focus {
  border-color: hsl(var(--ring));
  box-shadow: 0 0 0 3px hsl(var(--ring) / 0.1);
}

/* Accessibility Improvements */
.skip-to-content {
  position: absolute;
  top: -40px;
  left: 6px;
  background: hsl(var(--primary));
  color: hsl(var(--primary-foreground));
  padding: 8px;
  text-decoration: none;
  border-radius: 4px;
  z-index: 9999;
}

.skip-to-content:focus {
  top: 6px;
}

/* Reduced Motion Support */
@media (prefers-reduced-motion: reduce) {
  *,
  ::before,
  ::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
    scroll-behavior: auto !important;
  }
}

:root {
  /* Default Emerald Theme */
  --background: hsl(0, 0%, 100%);
  --foreground: hsl(20, 14.3%, 4.1%);
  --muted: hsl(60, 4.8%, 95.9%);
  --muted-foreground: hsl(25, 5.3%, 44.7%);
  --popover: hsl(0, 0%, 100%);
  --popover-foreground: hsl(20, 14.3%, 4.1%);
  --card: hsl(0, 0%, 100%);
  --card-foreground: hsl(20, 14.3%, 4.1%);
  --border: hsl(20, 5.9%, 90%);
  --input: hsl(20, 5.9%, 90%);
  --primary: hsl(143, 32%, 35%);
  --primary-foreground: hsl(0, 0%, 98%);
  --primary-dark: hsl(143, 32%, 25%);
  --secondary: hsl(60, 4.8%, 95.9%);
  --secondary-foreground: hsl(24, 9.8%, 10%);
  --accent: hsl(45, 70%, 53%);
  --accent-foreground: hsl(24, 9.8%, 10%);
  --destructive: hsl(0, 84.2%, 60.2%);
  --destructive-foreground: hsl(60, 9.1%, 97.8%);
  --ring: hsl(143, 32%, 35%);
  --radius: 0.5rem;
}

.dark {
  /* Default Dark Emerald Theme */
  --background: hsl(143, 32%, 8%);
  --foreground: hsl(0, 0%, 98%);
  --muted: hsl(143, 20%, 15%);
  --muted-foreground: hsl(143, 10%, 65%);
  --popover: hsl(143, 32%, 8%);
  --popover-foreground: hsl(0, 0%, 98%);
  --card: hsl(143, 20%, 10%);
  --card-foreground: hsl(0, 0%, 98%);
  --border: hsl(143, 20%, 15%);
  --input: hsl(143, 20%, 15%);
  --primary: hsl(143, 32%, 45%);
  --primary-foreground: hsl(0, 0%, 98%);
  --primary-dark: hsl(143, 32%, 35%);
  --secondary: hsl(143, 20%, 15%);
  --secondary-foreground: hsl(0, 0%, 98%);
  --accent: hsl(45, 70%, 63%);
  --accent-foreground: hsl(0, 0%, 98%);
  --destructive: hsl(0, 62.8%, 30.6%);
  --destructive-foreground: hsl(0, 0%, 98%);
  --ring: hsl(143, 20%, 85%);
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply font-sans antialiased bg-background text-foreground;
  }
}

@layer utilities {
  .islamic-pattern {
    background-image: url("data:image/svg+xml,%3Csvg width='40' height='40' viewBox='0 0 40 40' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='%23D4AF37' fill-opacity='0.05'%3E%3Cpath d='M20 20c0 11.046-8.954 20-20 20s-20-8.954-20-20 8.954-20 20-20 20 8.954 20 20zm-30 0c0 5.523 4.477 10 10 10s10-4.477 10-10-4.477-10-10-10-10 4.477-10 10z'/%3E%3C/g%3E%3C/svg%3E");
  }

  .compass-ring {
    background: conic-gradient(from 0deg, var(--primary), var(--accent), var(--primary), var(--accent), var(--primary));
  }

  .rtl {
    direction: rtl;
  }

  .font-amiri {
    font-family: 'Amiri', serif;
  }

  .touch-manipulation {
    touch-action: manipulation;
  }

  /* Mobile-specific optimizations */
  @media (max-width: 640px) {
    .mobile-padding {
      padding: 0.75rem;
    }
    
    .mobile-text {
      font-size: 0.875rem;
    }
    
    .mobile-button {
      min-height: 44px;
      min-width: 44px;
    }
  }

  /* Improve touch targets */
  .touch-target {
    min-height: 44px;
    min-width: 44px;
  }

  /* Safe area for devices with notches */
  .safe-area-top {
    padding-top: env(safe-area-inset-top);
  }

  .safe-area-bottom {
    padding-bottom: env(safe-area-inset-bottom);
  }
}

/* Custom animations */
@keyframes pulse-slow {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

@keyframes spin-slow {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

.animate-pulse-slow {
  animation: pulse-slow 3s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

.animate-spin-slow {
  animation: spin-slow 20s linear infinite;
}
