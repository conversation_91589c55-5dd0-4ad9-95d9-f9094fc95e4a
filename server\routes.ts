import type { Express } from "express";
import { createServer, type Server } from "http";
import { storage } from "./storage";
import { 
  insertUserPreferencesSchema,
  insertPrayerTrackingSchema,
  insertDhikrCountSchema,
  insertQuranProgressSchema
} from "@shared/schema";

export async function registerRoutes(app: Express): Promise<Server> {
  // User preferences
  app.get("/api/preferences/:userId", async (req, res) => {
    try {
      const userId = parseInt(req.params.userId);
      const preferences = await storage.getUserPreferences(userId);
      res.json(preferences || {});
    } catch (error) {
      res.status(500).json({ message: "Failed to get preferences" });
    }
  });

  app.put("/api/preferences/:userId", async (req, res) => {
    try {
      const userId = parseInt(req.params.userId);
      const preferences = insertUserPreferencesSchema.parse(req.body);
      const updated = await storage.updateUserPreferences(userId, preferences);
      res.json(updated);
    } catch (error) {
      res.status(400).json({ message: "Invalid preferences data" });
    }
  });

  // Prayer tracking
  app.get("/api/prayers/:userId", async (req, res) => {
    try {
      const userId = parseInt(req.params.userId);
      const date = req.query.date ? new Date(req.query.date as string) : new Date();
      const tracking = await storage.getPrayerTracking(userId, date);
      res.json(tracking);
    } catch (error) {
      res.status(500).json({ message: "Failed to get prayer tracking" });
    }
  });

  app.post("/api/prayers", async (req, res) => {
    try {
      const tracking = insertPrayerTrackingSchema.parse(req.body);
      const updated = await storage.updatePrayerTracking(tracking);
      res.json(updated);
    } catch (error) {
      res.status(400).json({ message: "Invalid prayer tracking data" });
    }
  });

  // Dhikr counts
  app.get("/api/dhikr/:userId", async (req, res) => {
    try {
      const userId = parseInt(req.params.userId);
      const date = req.query.date ? new Date(req.query.date as string) : new Date();
      const counts = await storage.getDhikrCounts(userId, date);
      res.json(counts);
    } catch (error) {
      res.status(500).json({ message: "Failed to get dhikr counts" });
    }
  });

  app.post("/api/dhikr", async (req, res) => {
    try {
      const dhikr = insertDhikrCountSchema.parse(req.body);
      const updated = await storage.updateDhikrCount(dhikr);
      res.json(updated);
    } catch (error) {
      res.status(400).json({ message: "Invalid dhikr data" });
    }
  });

  // Quran progress
  app.get("/api/quran/:userId", async (req, res) => {
    try {
      const userId = parseInt(req.params.userId);
      const progress = await storage.getQuranProgress(userId);
      res.json(progress);
    } catch (error) {
      res.status(500).json({ message: "Failed to get Quran progress" });
    }
  });

  app.post("/api/quran", async (req, res) => {
    try {
      const progress = insertQuranProgressSchema.parse(req.body);
      const updated = await storage.updateQuranProgress(progress);
      res.json(updated);
    } catch (error) {
      res.status(400).json({ message: "Invalid Quran progress data" });
    }
  });

  const httpServer = createServer(app);
  return httpServer;
}
