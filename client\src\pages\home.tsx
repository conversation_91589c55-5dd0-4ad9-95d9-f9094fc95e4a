import { useState, useEffect } from "react";
import { useTheme } from "@/components/theme-provider";
import { PrayerTimes } from "@/components/prayer-times";
import { QiblaCompass } from "@/components/qibla-compass";
import { QuranReader } from "@/components/quran-reader";
import { DigitalTasbih } from "@/components/digital-tasbih";
import { IslamicCalendar } from "@/components/islamic-calendar";
import { Statistics } from "@/components/statistics";
import { AdhkarPage } from "@/components/adhkar-page";
import { HadithPage } from "@/components/hadith-page";
import { FloatingMenu } from "@/components/floating-menu";
import Settings from "@/pages/settings";
import { Moon, Sun, Languages, ArrowLeft } from "lucide-react";
import { Button } from "@/components/ui/button";
import { themes } from "@/data/themes";

type Section = "prayer" | "qibla" | "quran" | "tasbih" | "calendar" | "stats" | "adhkar" | "hadith" | "settings";

export default function Home() {
  const { theme, toggleTheme } = useTheme();
  const [activeSection, setActiveSection] = useState<Section>("prayer");
  const [language, setLanguage] = useState<"en" | "ar">("en");
  const [location, setLocation] = useState({ city: "Riyadh", country: "Saudi Arabia" });
  const [currentTheme, setCurrentTheme] = useState("emerald");
  const [navigationHistory, setNavigationHistory] = useState<Section[]>(["prayer"]);

  useEffect(() => {
    document.documentElement.dir = language === "ar" ? "rtl" : "ltr";
    document.documentElement.lang = language;
  }, [language]);

  useEffect(() => {
    // Apply theme colors
    const selectedTheme = themes.find(t => t.id === currentTheme);
    if (selectedTheme) {
      const root = document.documentElement;
      const colors = theme === "dark" ? selectedTheme.darkColors : selectedTheme.colors;
      
      Object.entries(colors).forEach(([key, value]) => {
        root.style.setProperty(`--${key.replace(/([A-Z])/g, '-$1').toLowerCase()}`, value);
      });
    }
  }, [currentTheme, theme]);

  const toggleLanguage = () => {
    setLanguage(prev => prev === "en" ? "ar" : "en");
  };

  const handleSectionChange = (section: Section) => {
    setNavigationHistory(prev => [...prev, section]);
    setActiveSection(section);
  };

  const handleBack = () => {
    if (navigationHistory.length > 1) {
      const newHistory = [...navigationHistory];
      newHistory.pop(); // Remove current section
      const previousSection = newHistory[newHistory.length - 1];
      setNavigationHistory(newHistory);
      setActiveSection(previousSection);
    }
  };

  const renderActiveSection = () => {
    switch (activeSection) {
      case "prayer":
        return <PrayerTimes />;
      case "qibla":
        return <QiblaCompass />;
      case "quran":
        return <QuranReader language={language} />;
      case "tasbih":
        return <DigitalTasbih />;
      case "calendar":
        return <IslamicCalendar />;
      case "stats":
        return <Statistics />;
      case "adhkar":
        return <AdhkarPage onBack={handleBack} language={language} />;
      case "hadith":
        return <HadithPage onBack={handleBack} language={language} />;
      case "duaa":
        return <DuaaPage onBack={handleBack} language={language} />;
      case "settings":
        return <Settings onBack={handleBack} language={language} setLanguage={setLanguage} />;
      default:
        return <PrayerTimes />;
    }
  };

  const showHeader = !["adhkar", "hadith", "settings"].includes(activeSection);
  const showBackButton = navigationHistory.length > 1 && !["adhkar", "hadith", "settings"].includes(activeSection);

  if (!showHeader) {
    return (
      <div className="min-h-screen bg-background">
        {renderActiveSection()}
        <FloatingMenu 
          activeSection={activeSection} 
          onSectionChange={handleSectionChange}
          language={language}
        />
      </div>
    );
  }

  return (
    <div className="w-full bg-background min-h-screen relative overflow-x-hidden">
      {/* Header */}
      <header className="bg-primary text-primary-foreground p-4 islamic-pattern sticky top-0 z-40 shadow-lg">
        <div className="flex justify-between items-center max-w-full">
          <div className="flex items-center min-w-0 flex-1">
            {showBackButton && (
              <Button
                variant="ghost"
                size="sm"
                onClick={handleBack}
                className="mr-3 text-primary-foreground hover:bg-primary-foreground/20 flex-shrink-0"
              >
                <ArrowLeft className="h-5 w-5" />
              </Button>
            )}
            <div className="min-w-0 flex-1">
              <h1 className="text-lg sm:text-xl font-bold font-amiri truncate">
                {language === "ar" ? "إسلامي" : "Islamic"}
              </h1>
              <p className="text-xs sm:text-sm opacity-90 truncate">{location.city}, {location.country}</p>
            </div>
          </div>
          <div className="flex items-center space-x-2 flex-shrink-0">
            <Button
              variant="ghost"
              size="sm"
              onClick={toggleLanguage}
              className="p-2 rounded-lg bg-white bg-opacity-20 hover:bg-opacity-30 text-white"
            >
              <Languages className="h-4 w-4" />
            </Button>
            <Button
              variant="ghost"
              size="sm"
              onClick={toggleTheme}
              className="p-2 rounded-lg bg-white bg-opacity-20 hover:bg-opacity-30 text-white"
            >
              {theme === "dark" ? (
                <Sun className="h-4 w-4" />
              ) : (
                <Moon className="h-4 w-4" />
              )}
            </Button>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="pb-20 w-full min-h-[calc(100vh-80px)]">
        {renderActiveSection()}
      </main>

      {/* Floating Menu */}
      <FloatingMenu 
        activeSection={activeSection} 
        onSectionChange={handleSectionChange}
        language={language}
      />
    </div>
  );
}
