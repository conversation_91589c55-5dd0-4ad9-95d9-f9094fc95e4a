export interface Theme {
  id: string;
  name: string;
  arabicName: string;
  colors: {
    primary: string;
    primaryDark: string;
    secondary: string;
    accent: string;
    background: string;
    foreground: string;
    card: string;
    cardForeground: string;
    muted: string;
    mutedForeground: string;
    border: string;
    ring: string;
  };
  darkColors: {
    primary: string;
    primaryDark: string;
    secondary: string;
    accent: string;
    background: string;
    foreground: string;
    card: string;
    cardForeground: string;
    muted: string;
    mutedForeground: string;
    border: string;
    ring: string;
  };
}

export const themes: Theme[] = [
  {
    id: "emerald",
    name: "Emerald Islamic",
    arabicName: "الزمردي الإسلامي",
    colors: {
      primary: "hsl(143, 32%, 35%)",
      primaryDark: "hsl(143, 32%, 25%)",
      secondary: "hsl(60, 4.8%, 95.9%)",
      accent: "hsl(45, 70%, 53%)",
      background: "hsl(0, 0%, 100%)",
      foreground: "hsl(20, 14.3%, 4.1%)",
      card: "hsl(0, 0%, 100%)",
      cardForeground: "hsl(20, 14.3%, 4.1%)",
      muted: "hsl(60, 4.8%, 95.9%)",
      mutedForeground: "hsl(25, 5.3%, 44.7%)",
      border: "hsl(20, 5.9%, 90%)",
      ring: "hsl(143, 32%, 35%)"
    },
    darkColors: {
      primary: "hsl(143, 32%, 45%)",
      primaryDark: "hsl(143, 32%, 35%)",
      secondary: "hsl(143, 20%, 15%)",
      accent: "hsl(45, 70%, 63%)",
      background: "hsl(143, 32%, 8%)",
      foreground: "hsl(0, 0%, 98%)",
      card: "hsl(143, 20%, 10%)",
      cardForeground: "hsl(0, 0%, 98%)",
      muted: "hsl(143, 20%, 15%)",
      mutedForeground: "hsl(143, 10%, 65%)",
      border: "hsl(143, 20%, 15%)",
      ring: "hsl(143, 20%, 85%)"
    }
  },
  {
    id: "royal-blue",
    name: "Royal Blue",
    arabicName: "الأزرق الملكي",
    colors: {
      primary: "hsl(221, 83%, 53%)",
      primaryDark: "hsl(221, 83%, 43%)",
      secondary: "hsl(210, 40%, 95%)",
      accent: "hsl(45, 70%, 53%)",
      background: "hsl(0, 0%, 100%)",
      foreground: "hsl(222.2, 84%, 4.9%)",
      card: "hsl(0, 0%, 100%)",
      cardForeground: "hsl(222.2, 84%, 4.9%)",
      muted: "hsl(210, 40%, 95%)",
      mutedForeground: "hsl(215.4, 16.3%, 46.9%)",
      border: "hsl(214.3, 31.8%, 91.4%)",
      ring: "hsl(221, 83%, 53%)"
    },
    darkColors: {
      primary: "hsl(221, 83%, 63%)",
      primaryDark: "hsl(221, 83%, 53%)",
      secondary: "hsl(217.2, 32.6%, 17.5%)",
      accent: "hsl(45, 70%, 63%)",
      background: "hsl(222.2, 84%, 4.9%)",
      foreground: "hsl(210, 40%, 98%)",
      card: "hsl(222.2, 84%, 4.9%)",
      cardForeground: "hsl(210, 40%, 98%)",
      muted: "hsl(217.2, 32.6%, 17.5%)",
      mutedForeground: "hsl(215, 20.2%, 65.1%)",
      border: "hsl(217.2, 32.6%, 17.5%)",
      ring: "hsl(212.7, 26.8%, 83.9%)"
    }
  },
  {
    id: "sunset",
    name: "Sunset Orange",
    arabicName: "برتقالي الغروب",
    colors: {
      primary: "hsl(24, 95%, 53%)",
      primaryDark: "hsl(24, 95%, 43%)",
      secondary: "hsl(24, 40%, 95%)",
      accent: "hsl(45, 70%, 53%)",
      background: "hsl(0, 0%, 100%)",
      foreground: "hsl(20, 14.3%, 4.1%)",
      card: "hsl(0, 0%, 100%)",
      cardForeground: "hsl(20, 14.3%, 4.1%)",
      muted: "hsl(24, 40%, 95%)",
      mutedForeground: "hsl(25, 5.3%, 44.7%)",
      border: "hsl(24, 5.9%, 90%)",
      ring: "hsl(24, 95%, 53%)"
    },
    darkColors: {
      primary: "hsl(24, 95%, 63%)",
      primaryDark: "hsl(24, 95%, 53%)",
      secondary: "hsl(24, 32%, 17%)",
      accent: "hsl(45, 70%, 63%)",
      background: "hsl(20, 50%, 6%)",
      foreground: "hsl(0, 0%, 98%)",
      card: "hsl(24, 32%, 10%)",
      cardForeground: "hsl(0, 0%, 98%)",
      muted: "hsl(24, 32%, 17%)",
      mutedForeground: "hsl(24, 20%, 65%)",
      border: "hsl(24, 32%, 17%)",
      ring: "hsl(24, 95%, 85%)"
    }
  },
  {
    id: "purple-mystic",
    name: "Purple Mystic",
    arabicName: "البنفسجي الصوفي",
    colors: {
      primary: "hsl(262, 52%, 47%)",
      primaryDark: "hsl(262, 52%, 37%)",
      secondary: "hsl(262, 40%, 95%)",
      accent: "hsl(45, 70%, 53%)",
      background: "hsl(0, 0%, 100%)",
      foreground: "hsl(262, 14.3%, 4.1%)",
      card: "hsl(0, 0%, 100%)",
      cardForeground: "hsl(262, 14.3%, 4.1%)",
      muted: "hsl(262, 40%, 95%)",
      mutedForeground: "hsl(262, 5.3%, 44.7%)",
      border: "hsl(262, 5.9%, 90%)",
      ring: "hsl(262, 52%, 47%)"
    },
    darkColors: {
      primary: "hsl(262, 52%, 57%)",
      primaryDark: "hsl(262, 52%, 47%)",
      secondary: "hsl(262, 32%, 17%)",
      accent: "hsl(45, 70%, 63%)",
      background: "hsl(262, 50%, 6%)",
      foreground: "hsl(0, 0%, 98%)",
      card: "hsl(262, 32%, 10%)",
      cardForeground: "hsl(0, 0%, 98%)",
      muted: "hsl(262, 32%, 17%)",
      mutedForeground: "hsl(262, 20%, 65%)",
      border: "hsl(262, 32%, 17%)",
      ring: "hsl(262, 52%, 85%)"
    }
  }
];