import { useState } from "react";
import { Card } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { adhkarData, adhkarCategories } from "@/data/adhkar-data";
import { ArrowLeft, Play, Pause, Copy, Share2, BookOpen } from "lucide-react";
import { useToast } from "@/hooks/use-toast";

interface AdhkarPageProps {
  onBack: () => void;
  language: "en" | "ar";
}

export function AdhkarPage({ onBack, language }: AdhkarPageProps) {
  const [selectedCategory, setSelectedCategory] = useState("morning");
  const [playingDhikr, setPlayingDhikr] = useState<string | null>(null);
  const { toast } = useToast();

  const filteredAdhkar = adhkarData.filter(dhikr => dhikr.category === selectedCategory);
  const currentCategory = adhkarCategories.find(cat => cat.id === selectedCategory);

  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text);
    toast({
      title: language === "ar" ? "تم النسخ" : "Copied",
      description: language === "ar" ? "تم نسخ النص" : "Text copied to clipboard"
    });
  };

  const toggleAudio = (dhikrId: string) => {
    if (playingDhikr === dhikrId) {
      setPlayingDhikr(null);
    } else {
      setPlayingDhikr(dhikrId);
    }
  };

  return (
    <div className="min-h-screen bg-background">
      {/* Header */}
      <header className="bg-primary text-primary-foreground p-4 flex items-center">
        <Button variant="ghost" size="sm" onClick={onBack} className="mr-3 text-primary-foreground hover:bg-primary-foreground/20">
          <ArrowLeft className="h-5 w-5" />
        </Button>
        <div className="flex items-center space-x-3">
          <BookOpen className="h-6 w-6" />
          <h1 className="text-xl font-bold">
            {language === "ar" ? "الأذكار والأدعية" : "Adhkar & Supplications"}
          </h1>
        </div>
      </header>

      <div className="p-4">
        {/* Category Selection */}
        <Tabs value={selectedCategory} onValueChange={setSelectedCategory} className="w-full">
          <TabsList className="grid w-full grid-cols-2 lg:grid-cols-5 mb-6">
            {adhkarCategories.map((category) => (
              <TabsTrigger
                key={category.id}
                value={category.id}
                className="text-xs px-2 py-2"
              >
                {language === "ar" ? category.name : category.englishName}
              </TabsTrigger>
            ))}
          </TabsList>

          {adhkarCategories.map((category) => (
            <TabsContent key={category.id} value={category.id}>
              <div className="mb-4">
                <h2 className="text-2xl font-bold text-primary mb-2">
                  {language === "ar" ? category.name : category.englishName}
                </h2>
                <Badge variant="outline">
                  {filteredAdhkar.length} {language === "ar" ? "ذكر" : "adhkar"}
                </Badge>
              </div>

              <ScrollArea className="h-[calc(100vh-200px)]">
                <div className="space-y-4">
                  {filteredAdhkar.map((dhikr) => (
                    <Card key={dhikr.id} className="p-6">
                      {/* Arabic Text */}
                      <div className="mb-4 p-4 bg-muted rounded-xl rtl">
                        <p className="text-xl font-amiri leading-loose text-right text-foreground">
                          {dhikr.arabic}
                        </p>
                      </div>

                      {/* Transliteration */}
                      <div className="mb-4 p-3 bg-blue-50 dark:bg-blue-900/30 rounded-lg">
                        <p className="text-sm font-medium text-blue-800 dark:text-blue-200 italic">
                          {dhikr.transliteration}
                        </p>
                      </div>

                      {/* Translation */}
                      <div className="mb-4 p-3 bg-green-50 dark:bg-green-900/30 rounded-lg">
                        <p className="text-sm text-green-800 dark:text-green-200 leading-relaxed">
                          {dhikr.translation}
                        </p>
                      </div>

                      {/* Virtue */}
                      <div className="mb-4 p-3 bg-yellow-50 dark:bg-yellow-900/30 rounded-lg">
                        <h4 className="text-sm font-semibold text-yellow-800 dark:text-yellow-200 mb-1">
                          {language === "ar" ? "الفضل:" : "Virtue:"}
                        </h4>
                        <p className="text-sm text-yellow-700 dark:text-yellow-300">
                          {dhikr.virtue}
                        </p>
                      </div>

                      {/* Count & Source */}
                      <div className="flex items-center justify-between mb-4">
                        <div className="flex items-center space-x-2">
                          <Badge className="bg-primary text-primary-foreground">
                            {dhikr.count} {language === "ar" ? "مرة" : "times"}
                          </Badge>
                          <Badge variant="outline">
                            {dhikr.source}
                          </Badge>
                        </div>
                      </div>

                      {/* Action Buttons */}
                      <div className="flex items-center justify-between pt-3 border-t">
                        <div className="flex items-center space-x-2">
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => toggleAudio(dhikr.id)}
                            className="flex items-center space-x-1"
                          >
                            {playingDhikr === dhikr.id ? (
                              <Pause className="h-4 w-4" />
                            ) : (
                              <Play className="h-4 w-4" />
                            )}
                            <span className="text-xs">
                              {language === "ar" ? "تشغيل" : "Play"}
                            </span>
                          </Button>

                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => copyToClipboard(dhikr.arabic)}
                            className="flex items-center space-x-1"
                          >
                            <Copy className="h-4 w-4" />
                            <span className="text-xs">
                              {language === "ar" ? "نسخ" : "Copy"}
                            </span>
                          </Button>

                          <Button
                            variant="ghost"
                            size="sm"
                            className="flex items-center space-x-1"
                          >
                            <Share2 className="h-4 w-4" />
                            <span className="text-xs">
                              {language === "ar" ? "مشاركة" : "Share"}
                            </span>
                          </Button>
                        </div>

                        <div className="text-xs text-muted-foreground">
                          {language === "ar" ? "اضغط للتكرار" : "Tap to repeat"}
                        </div>
                      </div>
                    </Card>
                  ))}
                </div>
              </ScrollArea>
            </TabsContent>
          ))}
        </Tabs>
      </div>
    </div>
  );
}