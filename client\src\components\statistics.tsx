import { Card } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import { useQuery } from "@tanstack/react-query";

// Mock user ID for demo
const DEMO_USER_ID = 1;

export function Statistics() {
  const { data: prayerStats = [] } = useQuery({
    queryKey: ["/api/prayers", DEMO_USER_ID],
    queryFn: async () => {
      const res = await fetch(`/api/prayers/${DEMO_USER_ID}`);
      if (!res.ok) throw new Error('Failed to fetch prayer stats');
      return res.json();
    },
  });

  const { data: dhikrStats = [] } = useQuery({
    queryKey: ["/api/dhikr", DEMO_USER_ID],
    queryFn: async () => {
      const res = await fetch(`/api/dhikr/${DEMO_USER_ID}`);
      if (!res.ok) throw new Error('Failed to fetch dhikr stats');
      return res.json();
    },
  });

  const { data: quranStats = [] } = useQuery({
    queryKey: ["/api/quran", DEMO_USER_ID],
    queryFn: async () => {
      const res = await fetch(`/api/quran/${DEMO_USER_ID}`);
      if (!res.ok) throw new Error('Failed to fetch quran stats');
      return res.json();
    },
  });

  // Calculate statistics
  const today = new Date();
  const thisWeek = new Date(today.getTime() - 7 * 24 * 60 * 60 * 1000);
  const thisMonth = new Date(today.getFullYear(), today.getMonth(), 1);

  const prayersThisWeek = prayerStats.filter((p: any) => 
    new Date(p.date) >= thisWeek && p.completed
  ).length;

  const prayersThisMonth = prayerStats.filter((p: any) => 
    new Date(p.date) >= thisMonth && p.completed
  ).length;

  const totalDhikrCount = dhikrStats.reduce((sum: number, d: any) => sum + d.count, 0);
  const dhikrToday = dhikrStats.filter((d: any) => 
    new Date(d.date).toDateString() === today.toDateString()
  ).reduce((sum: number, d: any) => sum + d.count, 0);

  const totalPagesRead = quranStats.reduce((sum: number, q: any) => sum + (q.pagesRead || 0), 0);
  const totalTimeSpent = quranStats.reduce((sum: number, q: any) => sum + (q.timeSpent || 0), 0);

  // Calculate weekly prayer progress (5 prayers × 7 days = 35)
  const weeklyPrayerGoal = 35;
  const weeklyProgress = Math.min((prayersThisWeek / weeklyPrayerGoal) * 100, 100);

  // Group dhikr by type
  const dhikrByType = dhikrStats.reduce((acc: any, d: any) => {
    acc[d.dhikrType] = (acc[d.dhikrType] || 0) + d.count;
    return acc;
  }, {});

  const topDhikr = Object.entries(dhikrByType)
    .sort(([,a], [,b]) => (b as number) - (a as number))
    .slice(0, 3);

  const recentQuranSessions = quranStats
    .sort((a: any, b: any) => new Date(b.lastRead).getTime() - new Date(a.lastRead).getTime())
    .slice(0, 3);

  return (
    <section className="p-3 sm:p-4 w-full">
      <div className="space-y-3 sm:space-y-4 max-w-lg mx-auto">
        <h2 className="text-xl font-semibold text-gray-800 dark:text-white">
          Statistics
        </h2>
        
        {/* Prayer Statistics */}
        <Card className="p-6">
          <h3 className="text-lg font-semibold text-gray-800 dark:text-white mb-4">
            Prayer Tracking
          </h3>
          <div className="grid grid-cols-2 gap-4 mb-4">
            <div className="text-center">
              <p className="text-2xl font-bold text-emerald-600 dark:text-emerald-400">
                {prayersThisWeek}
              </p>
              <p className="text-sm text-gray-600 dark:text-gray-400">This Week</p>
            </div>
            <div className="text-center">
              <p className="text-2xl font-bold text-emerald-600 dark:text-emerald-400">
                {prayersThisMonth}
              </p>
              <p className="text-sm text-gray-600 dark:text-gray-400">This Month</p>
            </div>
          </div>
          <div>
            <div className="flex justify-between text-sm text-gray-600 dark:text-gray-400 mb-1">
              <span>Weekly Goal</span>
              <span>{Math.round(weeklyProgress)}%</span>
            </div>
            <Progress value={weeklyProgress} className="h-2" />
          </div>
        </Card>

        {/* Quran Reading Statistics */}
        <Card className="p-6">
          <h3 className="text-lg font-semibold text-gray-800 dark:text-white mb-4">
            Quran Reading
          </h3>
          <div className="grid grid-cols-2 gap-4 mb-4">
            <div className="text-center">
              <p className="text-2xl font-bold text-blue-600 dark:text-blue-400">
                {totalPagesRead}
              </p>
              <p className="text-sm text-gray-600 dark:text-gray-400">Pages Read</p>
            </div>
            <div className="text-center">
              <p className="text-2xl font-bold text-blue-600 dark:text-blue-400">
                {Math.round(totalTimeSpent / 60 * 10) / 10}
              </p>
              <p className="text-sm text-gray-600 dark:text-gray-400">Hours</p>
            </div>
          </div>
          {recentQuranSessions.length > 0 && (
            <div>
              <p className="text-sm text-gray-600 dark:text-gray-400 mb-2">Recent Sessions</p>
              <div className="space-y-2">
                {recentQuranSessions.map((session: any, index: number) => (
                  <div key={index} className="flex justify-between text-sm">
                    <span className="text-gray-800 dark:text-white">
                      Surah {session.surah}
                    </span>
                    <span className="text-gray-500">
                      {new Date(session.lastRead).toLocaleDateString()}
                    </span>
                  </div>
                ))}
              </div>
            </div>
          )}
        </Card>

        {/* Dhikr Statistics */}
        <Card className="p-6">
          <h3 className="text-lg font-semibold text-gray-800 dark:text-white mb-4">
            Dhikr Counter
          </h3>
          <div className="grid grid-cols-2 gap-4 mb-4">
            <div className="text-center">
              <p className="text-2xl font-bold text-yellow-600 dark:text-yellow-400">
                {totalDhikrCount}
              </p>
              <p className="text-sm text-gray-600 dark:text-gray-400">Total Count</p>
            </div>
            <div className="text-center">
              <p className="text-2xl font-bold text-yellow-600 dark:text-yellow-400">
                {dhikrToday}
              </p>
              <p className="text-sm text-gray-600 dark:text-gray-400">Today</p>
            </div>
          </div>
          {topDhikr.length > 0 && (
            <div>
              <p className="text-sm text-gray-600 dark:text-gray-400 mb-2">Most Used Dhikr</p>
              <div className="space-y-2">
                {topDhikr.map(([type, count], index) => (
                  <div key={index} className="flex justify-between text-sm">
                    <span className="text-gray-800 dark:text-white capitalize">
                      {(type as string).replace('-', ' ')}
                    </span>
                    <Badge variant="outline" className="text-yellow-600 dark:text-yellow-400">
                      {count as number}
                    </Badge>
                  </div>
                ))}
              </div>
            </div>
          )}
        </Card>

        {/* Weekly Summary */}
        <Card className="p-6">
          <h3 className="text-lg font-semibold text-gray-800 dark:text-white mb-4">
            This Week's Summary
          </h3>
          <div className="space-y-3">
            <div className="flex justify-between items-center">
              <span className="text-sm text-gray-600 dark:text-gray-400">
                Prayers Completed
              </span>
              <Badge className="bg-emerald-100 text-emerald-800 dark:bg-emerald-900 dark:text-emerald-200">
                {prayersThisWeek}/35
              </Badge>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-sm text-gray-600 dark:text-gray-400">
                Dhikr Count
              </span>
              <Badge className="bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200">
                {dhikrToday}
              </Badge>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-sm text-gray-600 dark:text-gray-400">
                Reading Time
              </span>
              <Badge className="bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200">
                {Math.round(totalTimeSpent / 60 * 10) / 10}h
              </Badge>
            </div>
          </div>
        </Card>
      </div>
    </section>
  );
}
