import { useState, useEffect } from "react";

interface OrientationData {
  heading: number | null;
  accuracy: "high" | "medium" | "low" | null;
}

export function useOrientation() {
  const [orientation, setOrientation] = useState<OrientationData>({
    heading: null,
    accuracy: null,
  });

  useEffect(() => {
    let isSupported = false;

    // Check for device orientation support
    if ("DeviceOrientationEvent" in window) {
      const handleOrientation = (event: DeviceOrientationEvent) => {
        if (event.webkitCompassHeading !== undefined) {
          // iOS devices
          setOrientation({
            heading: event.webkitCompassHeading,
            accuracy: event.webkitCompassAccuracy && event.webkitCompassAccuracy < 15 ? "high" : "medium",
          });
          isSupported = true;
        } else if (event.alpha !== null) {
          // Android devices
          const heading = event.alpha ? 360 - event.alpha : 0;
          setOrientation({
            heading,
            accuracy: "medium",
          });
          isSupported = true;
        }
      };

      // Request permission for iOS 13+
      if (typeof DeviceOrientationEvent !== 'undefined' && typeof (DeviceOrientationEvent as any).requestPermission === 'function') {
        (DeviceOrientationEvent as any).requestPermission()
          .then((response: string) => {
            if (response === 'granted') {
              window.addEventListener('deviceorientation', handleOrientation);
            }
          })
          .catch(() => {
            setOrientation({ heading: null, accuracy: "low" });
          });
      } else {
        // For other browsers
        window.addEventListener('deviceorientation', handleOrientation);
      }

      // Cleanup
      return () => {
        window.removeEventListener('deviceorientation', handleOrientation);
      };
    }

    // Fallback for unsupported devices
    if (!isSupported) {
      setOrientation({ heading: 0, accuracy: "low" });
    }
  }, []);

  return orientation;
}
