import { useEffect, useState } from 'react';

// Type definitions for Capacitor plugins
interface CapacitorGlobals {
  Capacitor?: {
    isNativePlatform: () => boolean;
    getPlatform: () => 'ios' | 'android' | 'web';
    isPluginAvailable: (name: string) => boolean;
  };
  Geolocation?: any;
  LocalNotifications?: any;
  Haptics?: any;
  Device?: any;
  StatusBar?: any;
  Storage?: any;
  Network?: any;
  Share?: any;
}

declare global {
  interface Window extends CapacitorGlobals {}
}

export function useCapacitor() {
  const [isNative, setIsNative] = useState(false);
  const [platform, setPlatform] = useState<'ios' | 'android' | 'web'>('web');
  const [isReady, setIsReady] = useState(false);

  useEffect(() => {
    const checkCapacitor = () => {
      if (window.Capacitor) {
        setIsNative(window.Capacitor.isNativePlatform());
        setPlatform(window.Capacitor.getPlatform());
        setIsReady(true);
      } else {
        // Fallback for web
        setIsNative(false);
        setPlatform('web');
        setIsReady(true);
      }
    };

    // Check immediately
    checkCapacitor();

    // Also check after a short delay in case Capacitor loads asynchronously
    const timeout = setTimeout(checkCapacitor, 100);

    return () => clearTimeout(timeout);
  }, []);

  const isPluginAvailable = (pluginName: string): boolean => {
    return window.Capacitor?.isPluginAvailable(pluginName) ?? false;
  };

  return {
    isNative,
    platform,
    isReady,
    isPluginAvailable,
    isIOS: platform === 'ios',
    isAndroid: platform === 'android',
    isWeb: platform === 'web'
  };
}

// Enhanced location hook for Capacitor
export function useCapacitorLocation() {
  const { isNative, isPluginAvailable } = useCapacitor();
  const [location, setLocation] = useState<{latitude: number; longitude: number} | null>(null);
  const [error, setError] = useState<string | null>(null);
  const [loading, setLoading] = useState(false);

  const getCurrentPosition = async () => {
    setLoading(true);
    setError(null);

    try {
      if (isNative && isPluginAvailable('Geolocation')) {
        // Use Capacitor Geolocation
        const coordinates = await window.Geolocation.getCurrentPosition({
          enableHighAccuracy: true,
          timeout: 10000
        });
        
        setLocation({
          latitude: coordinates.coords.latitude,
          longitude: coordinates.coords.longitude
        });
      } else {
        // Fallback to web API
        if (!navigator.geolocation) {
          throw new Error('Geolocation is not supported by this browser');
        }

        navigator.geolocation.getCurrentPosition(
          (position) => {
            setLocation({
              latitude: position.coords.latitude,
              longitude: position.coords.longitude
            });
          },
          (error) => {
            setError(error.message);
          },
          {
            enableHighAccuracy: true,
            timeout: 10000,
            maximumAge: 60000
          }
        );
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to get location');
    } finally {
      setLoading(false);
    }
  };

  return {
    location,
    error,
    loading,
    getCurrentPosition
  };
}

// Enhanced haptics hook for Capacitor
export function useCapacitorHaptics() {
  const { isNative, isPluginAvailable } = useCapacitor();

  const hapticFeedback = {
    light: async () => {
      if (isNative && isPluginAvailable('Haptics')) {
        await window.Haptics.impact({ style: 'light' });
      } else if ('vibrate' in navigator) {
        navigator.vibrate(50);
      }
    },
    medium: async () => {
      if (isNative && isPluginAvailable('Haptics')) {
        await window.Haptics.impact({ style: 'medium' });
      } else if ('vibrate' in navigator) {
        navigator.vibrate([100, 50, 100]);
      }
    },
    heavy: async () => {
      if (isNative && isPluginAvailable('Haptics')) {
        await window.Haptics.impact({ style: 'heavy' });
      } else if ('vibrate' in navigator) {
        navigator.vibrate([200, 100, 200]);
      }
    },
    success: async () => {
      if (isNative && isPluginAvailable('Haptics')) {
        await window.Haptics.notification({ type: 'success' });
      } else if ('vibrate' in navigator) {
        navigator.vibrate([100, 50, 100, 50, 300]);
      }
    }
  };

  return { hapticFeedback };
}

// Local notifications hook for Capacitor
export function useCapacitorNotifications() {
  const { isNative, isPluginAvailable } = useCapacitor();

  const scheduleNotification = async (options: {
    title: string;
    body: string;
    id: number;
    schedule?: Date;
  }) => {
    if (isNative && isPluginAvailable('LocalNotifications')) {
      await window.LocalNotifications.schedule({
        notifications: [
          {
            id: options.id,
            title: options.title,
            body: options.body,
            schedule: options.schedule ? { at: options.schedule } : undefined,
            sound: 'default',
            attachments: undefined,
            actionTypeId: '',
            extra: null
          }
        ]
      });
    } else if ('Notification' in window && Notification.permission === 'granted') {
      // Fallback to web notifications
      new Notification(options.title, {
        body: options.body,
        icon: '/icon-192x192.png'
      });
    }
  };

  const requestPermissions = async () => {
    if (isNative && isPluginAvailable('LocalNotifications')) {
      return await window.LocalNotifications.requestPermissions();
    } else if ('Notification' in window) {
      return await Notification.requestPermission();
    }
    return { display: 'denied' };
  };

  return {
    scheduleNotification,
    requestPermissions
  };
}