// Enhanced Haptic Feedback for Islamic App
export const hapticFeedback = {
  // Light feedback for tasbih counting
  light: () => {
    if ('vibrate' in navigator) {
      navigator.vibrate(50);
    }
  },
  
  // Medium feedback for prayer notifications
  medium: () => {
    if ('vibrate' in navigator) {
      navigator.vibrate([100, 50, 100]);
    }
  },
  
  // Strong feedback for completed dhikr
  strong: () => {
    if ('vibrate' in navigator) {
      navigator.vibrate([200, 100, 200, 100, 200]);
    }
  },
  
  // Success pattern for completed tasks
  success: () => {
    if ('vibrate' in navigator) {
      navigator.vibrate([100, 50, 100, 50, 300]);
    }
  }
};

// Enhanced audio feedback
export const audioFeedback = {
  playTasbihClick: () => {
    const audio = new Audio('data:audio/wav;base64,UklGRnoGAABXQVZFZm10IBAAAAABAAEAQB8AAEAfAAABAAgAZGF0YQoGAACBhYqFbF1fdJivrJBhNjVgodDbq2EcBj+a2/LDciUFLIHO8tiJNwgZaLvt559NEAxQp+PwtmMcBjiR1/LMeSwFJHfH8N2QQAoUXrTp66hVFApGn+DyvmcfCi2Czfnd');
    audio.volume = 0.3;
    audio.play().catch(() => {}); // Silently fail if audio not available
  },
  
  playNotification: () => {
    const audio = new Audio('data:audio/wav;base64,UklGRnoGAABXQVZFZm10IBAAAAABAAEAQB8AAEAfAAABAAgAZGF0YQoGAACBhYqFbF1fdJivrJBhNjVgodDbq2EcBj+a2/LDciUFLIHO8tiJNwgZaLvt559NEAxQp+PwtmMcBjiR1/LMeSwFJHfH8N2QQAoUXrTp66hVFApGn+DyvmcfCi2Czfnd');
    audio.volume = 0.5;
    audio.play().catch(() => {});
  }
};