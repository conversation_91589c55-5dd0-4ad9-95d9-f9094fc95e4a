import { useState, useEffect } from "react";
import { Card } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { useLocation } from "@/hooks/use-location";
import { useOrientation } from "@/hooks/use-orientation";
import { getQiblaDirection } from "@/lib/qibla";

export function QiblaCompass() {
  const { location, loading: locationLoading } = useLocation();
  const { heading, accuracy } = useOrientation();
  const [qiblaDirection, setQiblaDirection] = useState<number | null>(null);
  const [distance, setDistance] = useState<number | null>(null);

  useEffect(() => {
    if (location) {
      const { bearing, distance: dist } = getQiblaDirection(location.latitude, location.longitude);
      setQiblaDirection(bearing);
      setDistance(dist);
    }
  }, [location]);

  if (locationLoading) {
    return (
      <section className="p-4">
        <Card className="p-6 animate-pulse">
          <div className="h-6 bg-gray-200 dark:bg-gray-700 rounded mb-6"></div>
          <div className="w-64 h-64 mx-auto bg-gray-200 dark:bg-gray-700 rounded-full mb-6"></div>
          <div className="space-y-2">
            <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded"></div>
            <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded"></div>
          </div>
        </Card>
      </section>
    );
  }

  if (!location || qiblaDirection === null) {
    return (
      <section className="p-4">
        <Card className="p-6 text-center">
          <p className="text-gray-600 dark:text-gray-400">
            Unable to get location for Qibla direction calculation.
          </p>
        </Card>
      </section>
    );
  }

  const compassRotation = heading ? -heading : 0;
  const qiblaRotation = qiblaDirection - (heading || 0);

  const getDirectionText = (bearing: number) => {
    const directions = ["N", "NE", "E", "SE", "S", "SW", "W", "NW"];
    const index = Math.round(bearing / 45) % 8;
    return directions[index];
  };

  return (
    <section className="p-3 sm:p-4 w-full">
      <Card className="p-4 sm:p-6 mx-auto max-w-lg">
        <h2 className="text-lg sm:text-xl font-semibold text-gray-800 dark:text-white mb-4 sm:mb-6 text-center">
          Qibla Compass
        </h2>
        
        {/* Compass Container */}
        <div className="relative w-56 h-56 sm:w-64 sm:h-64 mx-auto mb-4 sm:mb-6">
          {/* Outer Ring */}
          <div className="absolute inset-0 rounded-full compass-ring p-2">
            <div 
              className="w-full h-full bg-white dark:bg-gray-900 rounded-full flex items-center justify-center transition-transform duration-300"
              style={{ transform: `rotate(${compassRotation}deg)` }}
            >
              {/* Inner Compass */}
              <div className="relative w-48 h-48 bg-gradient-to-br from-emerald-600 to-blue-600 rounded-full flex items-center justify-center">
                {/* Qibla Direction Indicator */}
                <div 
                  className="absolute w-1 h-8 bg-yellow-400 rounded-full transition-transform duration-300"
                  style={{ 
                    top: '8px',
                    left: '50%',
                    marginLeft: '-2px',
                    transform: `rotate(${qiblaRotation}deg)`,
                    transformOrigin: 'center 88px'
                  }}
                ></div>
                
                {/* Compass Needle */}
                <div className="absolute w-20 h-1 bg-red-500 rounded-full">
                  <div className="absolute right-0 w-0 h-0 border-l-4 border-l-red-500 border-t-2 border-b-2 border-t-transparent border-b-transparent"></div>
                </div>
                
                {/* Center Dot */}
                <div className="absolute w-4 h-4 bg-white rounded-full"></div>
              </div>
            </div>
          </div>
          
          {/* Direction Labels */}
          <div className="absolute top-2 left-1/2 transform -translate-x-1/2 text-sm font-semibold text-gray-600 dark:text-gray-300">N</div>
          <div className="absolute bottom-2 left-1/2 transform -translate-x-1/2 text-sm font-semibold text-gray-600 dark:text-gray-300">S</div>
          <div className="absolute left-2 top-1/2 transform -translate-y-1/2 text-sm font-semibold text-gray-600 dark:text-gray-300">W</div>
          <div className="absolute right-2 top-1/2 transform -translate-y-1/2 text-sm font-semibold text-gray-600 dark:text-gray-300">E</div>
        </div>

        {/* Qibla Information */}
        <div className="text-center space-y-2">
          <p className="text-lg font-semibold text-emerald-600 dark:text-emerald-400">
            {Math.round(qiblaDirection)}° {getDirectionText(qiblaDirection)}
          </p>
          {distance && (
            <p className="text-sm text-gray-600 dark:text-gray-400">
              Distance to Kaaba: {Math.round(distance)} km
            </p>
          )}
          <div className="flex items-center justify-center gap-2">
            <Badge variant={accuracy === "high" ? "default" : "secondary"}>
              Accuracy: {accuracy || "unknown"}
            </Badge>
            <p className="text-xs text-gray-500">
              Last updated: Just now
            </p>
          </div>
        </div>
      </Card>
    </section>
  );
}
