import { useState } from "react";
import { ArrowLeft, Search, BookOpen, Heart } from "lucide-react";
import { <PERSON><PERSON> } from "./ui/button";
import { Card } from "./ui/card";
import { Input } from "./ui/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "./ui/select";
import { Badge } from "./ui/badge";
import { duaaCollection, duaaCategories } from "../data/duaa-collection";

interface DuaaPageProps {
  onBack: () => void;
  language: "en" | "ar";
}

export function DuaaPage({ onBack, language }: DuaaPageProps) {
  const [selectedCategory, setSelectedCategory] = useState<string>("all");
  const [searchTerm, setSearchTerm] = useState("");

  const filteredDuaas = duaaCollection.filter(duaa => {
    const matchesCategory = selectedCategory === "all" || duaa.category === selectedCategory;
    const matchesSearch = searchTerm === "" || 
      duaa.translation.toLowerCase().includes(searchTerm.toLowerCase()) ||
      duaa.transliteration.toLowerCase().includes(searchTerm.toLowerCase()) ||
      duaa.occasion.toLowerCase().includes(searchTerm.toLowerCase());
    
    return matchesCategory && matchesSearch;
  });

  return (
    <section className="p-3 sm:p-4 w-full">
      <Card className="p-4 sm:p-6 mx-auto max-w-lg">
        <div className="flex justify-between items-center mb-4 sm:mb-6">
          <Button variant="ghost" size="sm" onClick={onBack} className="mr-2 flex-shrink-0">
            <ArrowLeft className="h-4 w-4" />
          </Button>
          <h2 className="text-lg sm:text-xl font-semibold text-gray-800 dark:text-white min-w-0 flex-1 text-center">
            {language === "ar" ? "مجموعة الأدعية المأثورة" : "Authentic Du'a Collection"}
          </h2>
          <div className="w-8 flex-shrink-0"></div>
        </div>

        {/* Search and Filter */}
        <div className="space-y-3 mb-6">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
            <Input
              placeholder={language === "ar" ? "ابحث في الأدعية..." : "Search du'as..."}
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-10"
            />
          </div>
          
          <Select value={selectedCategory} onValueChange={setSelectedCategory}>
            <SelectTrigger>
              <SelectValue placeholder={language === "ar" ? "اختر فئة" : "Select category"} />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">
                {language === "ar" ? "جميع الأدعية" : "All Du'as"}
              </SelectItem>
              {duaaCategories.map((category) => (
                <SelectItem key={category.id} value={category.id}>
                  {language === "ar" ? category.arabicName : category.name}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>

        {/* Statistics */}
        <div className="grid grid-cols-2 gap-3 mb-6">
          <div className="text-center p-3 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
            <BookOpen className="h-5 w-5 text-blue-600 dark:text-blue-400 mx-auto mb-1" />
            <p className="text-sm font-medium text-blue-800 dark:text-blue-200">
              {filteredDuaas.length}
            </p>
            <p className="text-xs text-blue-600 dark:text-blue-400">
              {language === "ar" ? "دعاء" : "Du'as"}
            </p>
          </div>
          <div className="text-center p-3 bg-green-50 dark:bg-green-900/20 rounded-lg">
            <Heart className="h-5 w-5 text-green-600 dark:text-green-400 mx-auto mb-1" />
            <p className="text-sm font-medium text-green-800 dark:text-green-200">
              {duaaCategories.length}
            </p>
            <p className="text-xs text-green-600 dark:text-green-400">
              {language === "ar" ? "فئة" : "Categories"}
            </p>
          </div>
        </div>

        {/* Du'a List */}
        <div className="space-y-4 max-h-96 overflow-y-auto">
          {filteredDuaas.length === 0 ? (
            <div className="text-center py-8 text-gray-500 dark:text-gray-400">
              <BookOpen className="h-12 w-12 mx-auto mb-3 opacity-50" />
              <p>{language === "ar" ? "لا توجد أدعية تطابق البحث" : "No du'as found matching your search"}</p>
            </div>
          ) : (
            filteredDuaas.map((duaa) => (
              <Card key={duaa.id} className="p-4 hover:shadow-md transition-shadow">
                <div className="space-y-3">
                  {/* Arabic Text */}
                  <div className="text-right">
                    <p className="text-lg font-amiri leading-relaxed text-gray-800 dark:text-white">
                      {duaa.arabic}
                    </p>
                  </div>
                  
                  {/* Transliteration */}
                  <div className="bg-gray-50 dark:bg-gray-800 p-3 rounded-lg">
                    <p className="text-sm italic text-gray-700 dark:text-gray-300">
                      {duaa.transliteration}
                    </p>
                  </div>
                  
                  {/* Translation */}
                  <div>
                    <p className="text-sm text-gray-700 dark:text-gray-300 leading-relaxed">
                      {duaa.translation}
                    </p>
                  </div>
                  
                  {/* Occasion */}
                  <div className="bg-blue-50 dark:bg-blue-900/20 p-2 rounded">
                    <p className="text-xs text-blue-800 dark:text-blue-200">
                      <strong>{language === "ar" ? "المناسبة:" : "Occasion:"}</strong> {duaa.occasion}
                    </p>
                  </div>
                  
                  {/* Virtue (if available) */}
                  {duaa.virtue && (
                    <div className="bg-green-50 dark:bg-green-900/20 p-2 rounded">
                      <p className="text-xs text-green-800 dark:text-green-200">
                        <strong>{language === "ar" ? "الفضل:" : "Virtue:"}</strong> {duaa.virtue}
                      </p>
                    </div>
                  )}
                  
                  {/* Source and Reference */}
                  <div className="flex justify-between items-center text-xs">
                    <div className="flex gap-2">
                      <Badge variant="secondary" className="text-xs">
                        {duaa.category}
                      </Badge>
                      <span className="text-blue-600 dark:text-blue-400">
                        {duaa.source}
                      </span>
                    </div>
                  </div>
                  
                  {duaa.reference && (
                    <p className="text-xs text-gray-500 dark:text-gray-400">
                      {language === "ar" ? "المرجع:" : "Reference:"} {duaa.reference}
                    </p>
                  )}
                </div>
              </Card>
            ))
          )}
        </div>
      </Card>
    </section>
  );
}