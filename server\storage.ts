import { 
  users, 
  userPreferences, 
  prayerTracking, 
  dhikrCounts, 
  quranProgress,
  type User, 
  type InsertUser,
  type UserPreferences,
  type InsertUserPreferences,
  type PrayerTracking,
  type InsertPrayerTracking,
  type DhikrCount,
  type InsertDhikrCount,
  type QuranProgress,
  type InsertQuranProgress
} from "@shared/schema";

export interface IStorage {
  getUser(id: number): Promise<User | undefined>;
  getUserByUsername(username: string): Promise<User | undefined>;
  createUser(user: InsertUser): Promise<User>;
  
  getUserPreferences(userId: number): Promise<UserPreferences | undefined>;
  updateUserPreferences(userId: number, preferences: Partial<InsertUserPreferences>): Promise<UserPreferences>;
  
  getPrayerTracking(userId: number, date: Date): Promise<PrayerTracking[]>;
  updatePrayerTracking(tracking: InsertPrayerTracking): Promise<PrayerTracking>;
  
  getDhikrCounts(userId: number, date: Date): Promise<DhikrCount[]>;
  updateDhikrCount(dhikr: InsertDhikrCount): Promise<DhikrCount>;
  
  getQuranProgress(userId: number): Promise<QuranProgress[]>;
  updateQuranProgress(progress: InsertQuranProgress): Promise<QuranProgress>;
}

export class MemStorage implements IStorage {
  private users: Map<number, User>;
  private userPreferences: Map<number, UserPreferences>;
  private prayerTracking: Map<string, PrayerTracking>;
  private dhikrCounts: Map<string, DhikrCount>;
  private quranProgress: Map<string, QuranProgress>;
  private currentId: number;

  constructor() {
    this.users = new Map();
    this.userPreferences = new Map();
    this.prayerTracking = new Map();
    this.dhikrCounts = new Map();
    this.quranProgress = new Map();
    this.currentId = 1;
  }

  async getUser(id: number): Promise<User | undefined> {
    return this.users.get(id);
  }

  async getUserByUsername(username: string): Promise<User | undefined> {
    return Array.from(this.users.values()).find(
      (user) => user.username === username,
    );
  }

  async createUser(insertUser: InsertUser): Promise<User> {
    const id = this.currentId++;
    const user: User = { ...insertUser, id };
    this.users.set(id, user);
    return user;
  }

  async getUserPreferences(userId: number): Promise<UserPreferences | undefined> {
    return this.userPreferences.get(userId);
  }

  async updateUserPreferences(userId: number, preferences: Partial<InsertUserPreferences>): Promise<UserPreferences> {
    const existing = this.userPreferences.get(userId);
    const updated: UserPreferences = {
      id: existing?.id || this.currentId++,
      userId,
      darkMode: false,
      language: "en",
      latitude: null,
      longitude: null,
      city: null,
      country: null,
      calculationMethod: "MuslimWorldLeague",
      madhab: "Shafi",
      ...existing,
      ...preferences,
    };
    this.userPreferences.set(userId, updated);
    return updated;
  }

  async getPrayerTracking(userId: number, date: Date): Promise<PrayerTracking[]> {
    const dateStr = date.toDateString();
    return Array.from(this.prayerTracking.values()).filter(
      (tracking) => tracking.userId === userId && tracking.date.toDateString() === dateStr
    );
  }

  async updatePrayerTracking(tracking: InsertPrayerTracking): Promise<PrayerTracking> {
    const key = `${tracking.userId}-${tracking.prayer}-${tracking.date.toDateString()}`;
    const existing = this.prayerTracking.get(key);
    const updated: PrayerTracking = {
      id: existing?.id || this.currentId++,
      userId: tracking.userId || null,
      prayer: tracking.prayer,
      date: tracking.date,
      completed: tracking.completed || null,
    };
    this.prayerTracking.set(key, updated);
    return updated;
  }

  async getDhikrCounts(userId: number, date: Date): Promise<DhikrCount[]> {
    const dateStr = date.toDateString();
    return Array.from(this.dhikrCounts.values()).filter(
      (dhikr) => dhikr.userId === userId && dhikr.date.toDateString() === dateStr
    );
  }

  async updateDhikrCount(dhikr: InsertDhikrCount): Promise<DhikrCount> {
    const key = `${dhikr.userId}-${dhikr.dhikrType}-${dhikr.date.toDateString()}`;
    const existing = this.dhikrCounts.get(key);
    const updated: DhikrCount = {
      id: existing?.id || this.currentId++,
      userId: dhikr.userId || null,
      dhikrType: dhikr.dhikrType,
      date: dhikr.date,
      count: (existing?.count || 0) + (dhikr.count || 1),
    };
    this.dhikrCounts.set(key, updated);
    return updated;
  }

  async getQuranProgress(userId: number): Promise<QuranProgress[]> {
    return Array.from(this.quranProgress.values()).filter(
      (progress) => progress.userId === userId
    );
  }

  async updateQuranProgress(progress: InsertQuranProgress): Promise<QuranProgress> {
    const key = `${progress.userId}-${progress.surah}`;
    const existing = this.quranProgress.get(key);
    const updated: QuranProgress = {
      id: existing?.id || this.currentId++,
      userId: progress.userId || null,
      surah: progress.surah,
      ayah: progress.ayah,
      pagesRead: progress.pagesRead || null,
      timeSpent: progress.timeSpent || null,
      lastRead: progress.lastRead,
    };
    this.quranProgress.set(key, updated);
    return updated;
  }
}

export const storage = new MemStorage();
